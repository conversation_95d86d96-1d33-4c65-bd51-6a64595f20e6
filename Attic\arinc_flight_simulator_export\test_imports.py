#!/usr/bin/env python3
"""
Test script to verify that all imports work correctly.
Run this script to diagnose import issues.
"""

import sys
from pathlib import Path

def get_current_dir():
    """Get current directory, handling cases where __file__ is not defined."""
    try:
        return Path(__file__).parent
    except NameError:
        return Path.cwd()

def check_python_paths():
    """Check and display Python package search paths."""
    print("=== Python Path Analysis ===")
    print(f"Python executable: {sys.executable}")
    print(f"Python version: {sys.version}")
    print(f"Current working directory: {Path.cwd()}")

    try:
        current_script_dir = Path(__file__).parent
        print(f"Script directory: {current_script_dir}")
    except NameError:
        print("Script directory: __file__ not available")

    print(f"\nPython sys.path ({len(sys.path)} entries):")
    for i, path in enumerate(sys.path):
        path_obj = Path(path)
        exists = "✓" if path_obj.exists() else "✗"
        is_dir = "DIR" if path_obj.is_dir() else "FILE" if path_obj.is_file() else "N/A"
        print(f"  {i:2d}: {exists} [{is_dir:4s}] {path}")

    # Check for simulator files in each path
    print(f"\nSearching for simulator files in sys.path:")
    simulator_files = ['ui.py', 'flight_model.py', 'arinc_flight_simulator.py']

    for path_str in sys.path:
        path_obj = Path(path_str)
        if path_obj.exists() and path_obj.is_dir():
            found_files = []
            for sim_file in simulator_files:
                if (path_obj / sim_file).exists():
                    found_files.append(sim_file)

            if found_files:
                print(f"  ✓ {path_obj}: Found {found_files}")

    print()

def test_imports():
    """Test all required imports for the ARINC Flight Simulator."""
    print("=== ARINC Flight Simulator Import Test ===")

    # First, show Python path information
    check_python_paths()

    # Test import utilities first
    try:
        from import_utils import setup_local_imports, validate_environment, print_import_debug_info
        print("✓ import_utils imported successfully")

        # Setup imports
        simulator_dir = setup_local_imports()
        print("✓ Local imports setup completed")
        if simulator_dir:
            print(f"✓ Simulator directory found: {simulator_dir}")

        # Show updated paths after setup
        print(f"\nUpdated sys.path after setup ({len(sys.path)} entries):")
        for i, path in enumerate(sys.path[:5]):  # Show first 5 entries
            print(f"  {i}: {path}")
        if len(sys.path) > 5:
            print(f"  ... and {len(sys.path) - 5} more entries")

        # Validate environment
        if validate_environment():
            print("✓ Environment validation passed")
        else:
            print("✗ Environment validation failed")
            return False

    except ImportError as e:
        print(f"✗ Failed to import import_utils: {e}")
        print("Using fallback import setup...")

        # Fallback setup
        current_dir = str(get_current_dir())
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
            print(f"Added to sys.path: {current_dir}")
    
    # Test individual module imports
    modules_to_test = [
        ('ui', 'UI module'),
        ('flight_model', 'Flight model module'),
        ('A429_utilities', 'ARINC 429 utilities module'),
        ('config_manager', 'Configuration manager module'),
        ('airports_data', 'Airports data module')
    ]
    
    failed_imports = []
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            print(f"✓ {description} imported successfully")
        except ImportError as e:
            print(f"✗ Failed to import {module_name}: {e}")
            failed_imports.append(module_name)
    
    # Test main simulator import
    try:
        from arinc_flight_simulator import ArincFlightSimulator
        print("✓ Main simulator class imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import main simulator: {e}")
        failed_imports.append('arinc_flight_simulator')
    
    # Test optional PbaPro import
    try:
        import ppbase
        print("✓ PbaPro (ppbase) imported successfully")
    except ImportError:
        print("ℹ PbaPro (ppbase) not available - this is expected in simulation-only mode")
    
    # Summary
    print("\n=== Import Test Summary ===")
    if failed_imports:
        print(f"✗ {len(failed_imports)} modules failed to import:")
        for module in failed_imports:
            print(f"  - {module}")
        print("\nDebugging information:")
        try:
            print_import_debug_info()
        except:
            print("Could not print debug info")
        return False
    else:
        print("✓ All required modules imported successfully!")
        print("The ARINC Flight Simulator should work correctly.")
        return True

def test_ui_creation():
    """Test if UI can be created without errors."""
    print("\n=== UI Creation Test ===")
    try:
        import ui
        root = ui.create_ui()
        print("✓ UI created successfully")
        
        # Test if we can access UI variables
        if hasattr(ui, 'start_var') and hasattr(ui, 'dest_var'):
            print("✓ UI variables accessible")
        else:
            print("⚠ UI variables may not be properly initialized")
        
        # Don't start mainloop in test
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ Failed to create UI: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing ARINC Flight Simulator imports...\n")
    
    import_success = test_imports()
    
    if import_success:
        ui_success = test_ui_creation()
        
        if ui_success:
            print("\n🎉 All tests passed! The simulator should work correctly.")
        else:
            print("\n⚠ Import tests passed but UI creation failed.")
    else:
        print("\n❌ Import tests failed. Please fix the import issues before running the simulator.")
    
    print("\nTo run the simulator, use: python run_simulator.py")
