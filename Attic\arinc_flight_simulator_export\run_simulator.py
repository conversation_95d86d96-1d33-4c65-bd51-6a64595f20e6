#!/usr/bin/env python3
"""
ARINC Flight Simulator Launcher
Simple script to start the ARINC Flight Simulator application.
"""

import sys
import os

def main():
    """Launch the ARINC Flight Simulator."""
    # Setup imports using import utilities if available
    try:
        from import_utils import setup_local_imports, validate_environment
        setup_local_imports()
        if not validate_environment():
            sys.exit(1)
    except ImportError:
        # Fallback to manual setup
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)

    try:
        # Import and run the simulator
        from arinc_flight_simulator import ArincFlightSimulator

        print("Starting ARINC Flight Simulator...")
        print("Close the GUI window to exit the application.")

        # Create and start the simulator
        simulator = ArincFlightSimulator()
        simulator.root.mainloop()

    except ImportError as e:
        print(f"ERROR: Failed to import required modules: {e}")
        current_dir = os.path.dirname(os.path.abspath(__file__))
        print(f"Current directory: {current_dir}")
        print(f"Python path: {sys.path}")
        print("\nRequired files:")
        print("- arinc_flight_simulator.py")
        print("- ui.py")
        print("- flight_model.py")
        print("- A429_utilities.py")
        print("- airports_data.py")
        print("- config_manager.py")

        # List files in current directory for debugging
        print(f"\nFiles in {current_dir}:")
        try:
            for file in sorted(os.listdir(current_dir)):
                if file.endswith('.py'):
                    print(f"  {file}")
        except Exception:
            print("  Could not list files")

        sys.exit(1)

    except Exception as e:
        print(f"Error starting simulator: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
