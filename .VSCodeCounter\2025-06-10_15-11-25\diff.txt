Date : 2025-06-10 15:11:25
Directory : c:\Users\<USER>\Desktop\A429DataSimulator
Total : 62 files,  3233 codes, 769 comments, 867 blanks, all 4869 lines

Languages
+------------------+------------+------------+------------+------------+------------+
| language         | files      | code       | comment    | blank      | total      |
+------------------+------------+------------+------------+------------+------------+
| Python           |         47 |      2,630 |        769 |        682 |      4,081 |
| Markdown         |          5 |        532 |          0 |        173 |        705 |
| Ini              |          5 |         67 |          0 |         11 |         78 |
| Batch            |          3 |          4 |          0 |          1 |          5 |
| pip requirements |          2 |          0 |          0 |          0 |          0 |
+------------------+------------+------------+------------+------------+------------+

Directories
+-----------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| path                                                                                                            | files      | code       | comment    | blank      | total      |
+-----------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| .                                                                                                               |         62 |      3,233 |        769 |        867 |      4,869 |
| . (Files)                                                                                                       |          3 |        269 |          7 |         67 |        343 |
| Attic                                                                                                           |         29 |      3,040 |        502 |        747 |      4,289 |
| Attic (Files)                                                                                                   |          7 |        503 |         78 |         95 |        676 |
| Attic\arinc_flight_simulator_export                                                                             |         22 |      2,537 |        424 |        652 |      3,613 |
| arinc_flight_simulator_export                                                                                   |         11 |     -1,347 |       -229 |       -321 |     -1,897 |
| src                                                                                                             |         16 |      1,107 |        414 |        303 |      1,824 |
| src\arinc_flight_simulator                                                                                      |         16 |      1,107 |        414 |        303 |      1,824 |
| src\arinc_flight_simulator (Files)                                                                              |          3 |         75 |         42 |         25 |        142 |
| src\arinc_flight_simulator\arinc                                                                                |          2 |         38 |         56 |         20 |        114 |
| src\arinc_flight_simulator\config                                                                               |          2 |         22 |          5 |          4 |         31 |
| src\arinc_flight_simulator\core                                                                                 |          5 |        709 |        243 |        206 |      1,158 |
| src\arinc_flight_simulator\data                                                                                 |          2 |        178 |         36 |         12 |        226 |
| src\arinc_flight_simulator\ui                                                                                   |          2 |         85 |         32 |         36 |        153 |
| tests                                                                                                           |          3 |        164 |         75 |         71 |        310 |
+-----------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+

Files
+-----------------------------------------------------------------------------------------------------------------+------------------+------------+------------+------------+------------+
| filename                                                                                                        | language         | code       | comment    | blank      | total      |
+-----------------------------------------------------------------------------------------------------------------+------------------+------------+------------+------------+------------+
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\A429_utilities.py                                       | Python           |         35 |         24 |         13 |         72 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\airports_data.py                                        | Python           |        174 |          7 |          5 |        186 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator.py                               | Python           |        137 |         37 |         40 |        214 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\A429_utilities.py         | Python           |         33 |         18 |         12 |         63 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\IMPORT_IMPROVEMENTS.md    | Markdown         |        117 |          0 |         40 |        157 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\PATH_DIAGNOSTICS_GUIDE.md | Markdown         |        160 |          0 |         61 |        221 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\README.md                 | Markdown         |        259 |          0 |         80 |        339 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\__init__.py               | Python           |         17 |          9 |          4 |         30 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\airports_data.py          | Python           |        177 |          7 |          5 |        189 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\arinc_flight_simulator.py | Python           |        311 |         80 |         91 |        482 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\config_manager.py         | Python           |        476 |         79 |         90 |        645 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\diagnose_paths.py         | Python           |        175 |         30 |         40 |        245 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\flight_model.py           | Python           |         90 |         19 |         25 |        134 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\import_utils.py           | Python           |         74 |         43 |         25 |        142 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\requirements.txt          | pip requirements |          0 |         11 |          3 |         14 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\run_simulator.bat         | Batch            |         20 |          3 |          4 |         27 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\run_simulator.py          | Python           |         45 |         11 |         11 |         67 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\setup_environment.py      | Python           |        192 |         27 |         43 |        262 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\simulator_config.ini      | Ini              |         22 |          0 |          4 |         26 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\start_simulator.bat       | Batch            |          4 |          0 |          1 |          5 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\test_continuous_noise.py  | Python           |         63 |         15 |         18 |         96 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\test_imports.py           | Python           |        132 |         22 |         33 |        187 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\test_margin_effects.py    | Python           |         31 |          9 |         14 |         54 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\test_string_execution.py  | Python           |         68 |         18 |         23 |        109 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\arinc_flight_simulator_export\ui.py                     | Python           |         71 |         23 |         25 |        119 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\flight_model.py                                         | Python           |         78 |          8 |         21 |        107 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\main.py                                                 | Python           |         15 |          0 |          2 |         17 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\simulator_config.ini                                    | Ini              |         22 |          0 |          4 |         26 |
| c:\Users\<USER>\Desktop\A429DataSimulator\Attic\ui.py                                                   | Python           |         42 |          2 |         10 |         54 |
| c:\Users\<USER>\Desktop\A429DataSimulator\README.md                                                     | Markdown         |        194 |          0 |         59 |        253 |
| c:\Users\<USER>\Desktop\A429DataSimulator\arinc_flight_simulator_export\A429_utilities.py               | Python           |        -33 |        -18 |        -12 |        -63 |
| c:\Users\<USER>\Desktop\A429DataSimulator\arinc_flight_simulator_export\README.md                       | Markdown         |       -198 |          0 |        -67 |       -265 |
| c:\Users\<USER>\Desktop\A429DataSimulator\arinc_flight_simulator_export\airports_data.py                | Python           |       -177 |         -7 |         -5 |       -189 |
| c:\Users\<USER>\Desktop\A429DataSimulator\arinc_flight_simulator_export\arinc_flight_simulator.py       | Python           |       -281 |        -75 |        -85 |       -441 |
| c:\Users\<USER>\Desktop\A429DataSimulator\arinc_flight_simulator_export\config_manager.py               | Python           |       -445 |        -76 |        -87 |       -608 |
| c:\Users\<USER>\Desktop\A429DataSimulator\arinc_flight_simulator_export\flight_model.py                 | Python           |        -78 |         -8 |        -21 |       -107 |
| c:\Users\<USER>\Desktop\A429DataSimulator\arinc_flight_simulator_export\requirements.txt                | pip requirements |          0 |        -11 |         -3 |        -14 |
| c:\Users\<USER>\Desktop\A429DataSimulator\arinc_flight_simulator_export\run_simulator.bat               | Batch            |        -20 |         -3 |         -4 |        -27 |
| c:\Users\<USER>\Desktop\A429DataSimulator\arinc_flight_simulator_export\run_simulator.py                | Python           |        -23 |         -8 |         -8 |        -39 |
| c:\Users\<USER>\Desktop\A429DataSimulator\arinc_flight_simulator_export\simulator_config.ini            | Ini              |        -21 |          0 |         -4 |        -25 |
| c:\Users\<USER>\Desktop\A429DataSimulator\arinc_flight_simulator_export\ui.py                           | Python           |        -71 |        -23 |        -25 |       -119 |
| c:\Users\<USER>\Desktop\A429DataSimulator\setup.py                                                      | Python           |         53 |          7 |          4 |         64 |
| c:\Users\<USER>\Desktop\A429DataSimulator\simulator_config.ini                                          | Ini              |         22 |          0 |          4 |         26 |
| c:\Users\<USER>\Desktop\A429DataSimulator\src\arinc_flight_simulator\__init__.py                        | Python           |         15 |         16 |          4 |         35 |
| c:\Users\<USER>\Desktop\A429DataSimulator\src\arinc_flight_simulator\__main__.py                        | Python           |          3 |          5 |          3 |         11 |
| c:\Users\<USER>\Desktop\A429DataSimulator\src\arinc_flight_simulator\arinc\__init__.py                  | Python           |          4 |          5 |          3 |         12 |
| c:\Users\<USER>\Desktop\A429DataSimulator\src\arinc_flight_simulator\arinc\utilities.py                 | Python           |         34 |         51 |         17 |        102 |
| c:\Users\<USER>\Desktop\A429DataSimulator\src\arinc_flight_simulator\config\__init__.py                 | Python           |          0 |          5 |          1 |          6 |
| c:\Users\<USER>\Desktop\A429DataSimulator\src\arinc_flight_simulator\config\default.ini                 | Ini              |         22 |          0 |          3 |         25 |
| c:\Users\<USER>\Desktop\A429DataSimulator\src\arinc_flight_simulator\core\__init__.py                   | Python           |          9 |          5 |          3 |         17 |
| c:\Users\<USER>\Desktop\A429DataSimulator\src\arinc_flight_simulator\core\config.py                     | Python           |        219 |         43 |         48 |        310 |
| c:\Users\<USER>\Desktop\A429DataSimulator\src\arinc_flight_simulator\core\flight_model.py               | Python           |         97 |         72 |         33 |        202 |
| c:\Users\<USER>\Desktop\A429DataSimulator\src\arinc_flight_simulator\core\simulator.py                  | Python           |        292 |         95 |         91 |        478 |
| c:\Users\<USER>\Desktop\A429DataSimulator\src\arinc_flight_simulator\core\simulator_methods.py          | Python           |         92 |         28 |         31 |        151 |
| c:\Users\<USER>\Desktop\A429DataSimulator\src\arinc_flight_simulator\data\__init__.py                   | Python           |          6 |          5 |          3 |         14 |
| c:\Users\<USER>\Desktop\A429DataSimulator\src\arinc_flight_simulator\data\airports.py                   | Python           |        172 |         31 |          9 |        212 |
| c:\Users\<USER>\Desktop\A429DataSimulator\src\arinc_flight_simulator\main.py                            | Python           |         57 |         21 |         18 |         96 |
| c:\Users\<USER>\Desktop\A429DataSimulator\src\arinc_flight_simulator\ui\__init__.py                     | Python           |          9 |          5 |          3 |         17 |
| c:\Users\<USER>\Desktop\A429DataSimulator\src\arinc_flight_simulator\ui\interface.py                    | Python           |         76 |         27 |         33 |        136 |
| c:\Users\<USER>\Desktop\A429DataSimulator\tests\__init__.py                                             | Python           |          0 |          5 |          1 |          6 |
| c:\Users\<USER>\Desktop\A429DataSimulator\tests\test_arinc_utilities.py                                 | Python           |         91 |         40 |         41 |        172 |
| c:\Users\<USER>\Desktop\A429DataSimulator\tests\test_flight_model.py                                    | Python           |         73 |         30 |         29 |        132 |
| Total                                                                                                           |                  |      3,233 |        769 |        867 |      4,869 |
+-----------------------------------------------------------------------------------------------------------------+------------------+------------+------------+------------+------------+