#!/usr/bin/env python3
"""
Test script to verify the continuous noise implementation.
This script tests that the noise function produces smooth, continuous values
without jumps between calls.
"""

from flight_model import simulate_flight_data

def test_continuous_noise():
    """Test that the noise function produces continuous values."""
    
    # Test parameters
    start_airport = "EDDF"
    dest_airport = "KJFK"
    random_seed = 123
    movement_margin = 1.0
    
    # Generate data points over time
    time_points = [i * 0.5 for i in range(100)]  # 0 to 50 seconds, every 0.5 seconds
    altitudes = []
    ground_speeds = []
    headings = []
    
    print("Testing continuous noise function...")
    print(f"Movement margin: {movement_margin}")
    print(f"Random seed: {random_seed}")
    print(f"Time range: {time_points[0]} to {time_points[-1]} seconds")
    print()
    
    for time_sec in time_points:
        flight_data = simulate_flight_data(
            start_airport, 
            dest_airport, 
            int(time_sec), 
            random_seed, 
            movement_margin
        )
        
        altitudes.append(flight_data["Altitude_of_aircraft_ft"])
        ground_speeds.append(flight_data["Ground_Speed_knots"])
        headings.append(flight_data["Heading_degrees"])
    
    # Test with movement_margin = 0 (should have no noise)
    print("Testing with movement_margin = 0 (no noise)...")
    altitudes_no_noise = []
    for time_sec in time_points[:10]:  # Just test first 10 points
        flight_data = simulate_flight_data(
            start_airport, 
            dest_airport, 
            int(time_sec), 
            random_seed, 
            0.0  # No noise
        )
        altitudes_no_noise.append(flight_data["Altitude_of_aircraft_ft"])
    
    # Check that no-noise values are identical for same flight phase
    print(f"No-noise altitudes (first 10): {altitudes_no_noise[:5]}...")
    
    # Calculate differences between consecutive values to check continuity
    alt_diffs = [abs(altitudes[i+1] - altitudes[i]) for i in range(len(altitudes)-1)]
    speed_diffs = [abs(ground_speeds[i+1] - ground_speeds[i]) for i in range(len(ground_speeds)-1)]
    heading_diffs = [abs(headings[i+1] - headings[i]) for i in range(len(headings)-1)]

    print(f"\nContinuity analysis:")
    print(f"Altitude differences - Max: {max(alt_diffs):.2f}, Mean: {sum(alt_diffs)/len(alt_diffs):.2f}")
    print(f"Ground speed differences - Max: {max(speed_diffs):.2f}, Mean: {sum(speed_diffs)/len(speed_diffs):.2f}")
    print(f"Heading differences - Max: {max(heading_diffs):.2f}, Mean: {sum(heading_diffs)/len(heading_diffs):.2f}")
    
    # Test idempotency - same inputs should give same outputs
    print(f"\nTesting idempotency...")
    test_time = 25
    result1 = simulate_flight_data(start_airport, dest_airport, test_time, random_seed, movement_margin)
    result2 = simulate_flight_data(start_airport, dest_airport, test_time, random_seed, movement_margin)
    
    print(f"Same inputs give same outputs: {result1['Altitude_of_aircraft_ft'] == result2['Altitude_of_aircraft_ft']}")
    
    # Show some sample values
    print(f"\nSample values at different times:")
    for i in [0, 10, 20, 30, 40]:
        if i < len(time_points):
            print(f"Time {time_points[i]:4.1f}s: Alt={altitudes[i]:7.1f}ft, Speed={ground_speeds[i]:5.1f}kts, Heading={headings[i]:5.1f}°")
    
    # Show data trend (simple text-based visualization)
    print(f"\nData trend visualization (every 10th point):")
    print("Time(s)  Altitude(ft)  Speed(kts)  Heading(°)")
    print("-" * 45)
    for i in range(0, len(time_points), 10):
        if i < len(altitudes):
            print(f"{time_points[i]:6.1f}  {altitudes[i]:9.1f}  {ground_speeds[i]:8.1f}  {headings[i]:7.1f}")
    
    print("\nTest completed!")

if __name__ == "__main__":
    test_continuous_noise()
