#!/usr/bin/env python3
"""
Launcher script for the installed ARINC Flight Simulator package.

This script ONLY runs the installed package version and will not fall back
to the development version in the src directory.
"""

import sys
import os

def main():
    """Run the installed ARINC Flight Simulator package only."""
    # Remove current directory from Python path to avoid importing local src
    # Get current directory, handling cases where __file__ might not be defined
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
    except NameError:
        # __file__ is not defined (e.g., when run as string), use current working directory
        current_dir = os.getcwd()
    src_dir = os.path.join(current_dir, 'src')
    
    # Clean up sys.path to avoid local imports
    original_path = sys.path.copy()
    sys.path = [p for p in sys.path if not (p == current_dir or p == src_dir or p == '')]
    
    try:
        # Try to import the installed package only
        import arinc_flight_simulator
        
        # Verify this is truly an installed package
        module_path = arinc_flight_simulator.__file__
        if current_dir in module_path:
            raise ImportError("Local development version detected")
        
        print(f"SUCCESS: Running installed ARINC Flight Simulator v{arinc_flight_simulator.__version__}")
        print(f"   Package location: {os.path.dirname(module_path)}")
        print()
        
        # Run the installed package
        from arinc_flight_simulator.main import main as simulator_main
        return simulator_main()
        
    except ImportError:
        # Restore original path
        sys.path = original_path
        
        print("ERROR: ARINC Flight Simulator package is not installed or not accessible.")
        print()
        print("This script only runs the installed package version.")
        print()
        print("To install the package, run:")
        print("  pip install dist/arinc_flight_simulator-1.0.0-py3-none-any.whl")
        print()
        print("Or install from the current directory:")
        print("  pip install .")
        print()
        print("After installation, you can run the simulator with:")
        print("  arinc-simulator")
        print("  or")
        print("  python -m arinc_flight_simulator")
        print("  or")
        print("  python run_installed_simulator.py")
        print()
        print("To run the development version, use:")
        print("  python run_simulator.py")
        return 1

if __name__ == "__main__":
    exit_code = main()
    # Only call sys.exit() if there was an error (non-zero exit code)
    # This prevents terminating the calling program when the GUI is closed normally
    if exit_code != 0:
        sys.exit(exit_code)
