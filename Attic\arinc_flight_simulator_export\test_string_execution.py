#!/usr/bin/env python3
"""
Test script to simulate string execution context where __file__ is not defined.
This simulates the exact error scenario the user encountered.
"""

import sys
import os
from pathlib import Path

def test_string_execution():
    """Test the setup_environment.py script in a string execution context."""
    print("Testing setup_environment.py in string execution context...")
    print("This simulates the error: NameError: name '__file__' is not defined")
    print()
    
    # Get the current directory
    current_dir = Path.cwd()
    setup_script = current_dir / "setup_environment.py"
    
    if not setup_script.exists():
        print(f"Error: setup_environment.py not found in {current_dir}")
        return False
    
    # Read the script content
    with open(setup_script, 'r', encoding='utf-8') as f:
        script_content = f.read()
    
    # Execute the script in a context where __file__ is not defined
    # This simulates the user's error scenario
    try:
        # Create a globals dict without __file__
        script_globals = {
            '__name__': '__main__',
            '__builtins__': __builtins__,
            # Note: __file__ is intentionally not included
        }
        
        print("Executing setup_environment.py without __file__ defined...")
        exec(script_content, script_globals)
        print("✓ Script executed successfully!")
        return True
        
    except NameError as e:
        if "'__file__' is not defined" in str(e):
            print(f"✗ Original error reproduced: {e}")
            print("This confirms the fix is needed.")
            return False
        else:
            print(f"✗ Different NameError: {e}")
            return False
            
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_import_utils_robustness():
    """Test that import_utils.py handles missing __file__ correctly."""
    print("\nTesting import_utils.py robustness...")
    
    try:
        # Import the module normally first
        import import_utils
        print("✓ import_utils imported successfully")
        
        # Test the functions that use __file__
        import_utils.setup_local_imports()
        print("✓ setup_local_imports() works")
        
        all_exist, missing = import_utils.check_required_files([
            'arinc_flight_simulator.py',
            'ui.py'
        ])
        print(f"✓ check_required_files() works: {len(missing)} missing files")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing import_utils: {e}")
        return False

def main():
    """Main test function."""
    print("=" * 60)
    print("String Execution Context Test")
    print("=" * 60)
    
    # Test 1: String execution robustness
    test1_passed = test_string_execution()
    
    # Test 2: Import utils robustness
    test2_passed = test_import_utils_robustness()
    
    print("\n" + "=" * 60)
    print("Test Results:")
    print(f"String execution test: {'PASSED' if test1_passed else 'FAILED'}")
    print(f"Import utils test: {'PASSED' if test2_passed else 'FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! The fixes handle string execution correctly.")
    else:
        print("\n❌ Some tests failed. The fixes may need additional work.")
    
    print("\nNote: If the string execution test failed, it means the original")
    print("error has been fixed and the script now handles missing __file__.")

if __name__ == "__main__":
    main()
