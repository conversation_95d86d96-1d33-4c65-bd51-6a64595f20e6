#!/usr/bin/env python3
"""
Environment setup script for ARINC Flight Simulator.
This script helps diagnose and fix common environment issues.
"""

import sys
import os
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    print("=== Python Version Check ===")
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("⚠ Warning: Python 3.7 or higher is recommended")
        return False
    else:
        print("✓ Python version is compatible")
        return True

def check_required_packages():
    """Check if required packages are installed."""
    print("\n=== Required Packages Check ===")
    
    required_packages = [
        ('tkinter', 'tkinter'),
        ('configparser', 'configparser'),
        ('pathlib', 'pathlib')
    ]
    
    missing_packages = []
    
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✓ {package_name} is available")
        except ImportError:
            print(f"✗ {package_name} is missing")
            missing_packages.append(package_name)
    
    # Special check for tkinter on Linux
    if 'tkinter' in missing_packages and sys.platform.startswith('linux'):
        print("\nOn Linux, you may need to install tkinter separately:")
        print("  Ubuntu/Debian: sudo apt-get install python3-tk")
        print("  CentOS/RHEL: sudo yum install tkinter")
        print("  Fedora: sudo dnf install python3-tkinter")
    
    return len(missing_packages) == 0

def check_file_structure():
    """Check if all required files are present."""
    print("\n=== File Structure Check ===")

    # Handle cases where __file__ is not defined
    try:
        current_dir = Path(__file__).parent
    except NameError:
        # Fallback to current working directory
        current_dir = Path.cwd()
        print(f"Note: Using current working directory: {current_dir}")

    required_files = [
        'arinc_flight_simulator.py',
        'ui.py',
        'flight_model.py',
        'A429_utilities.py',
        'airports_data.py',
        'config_manager.py',
        'import_utils.py',
        'run_simulator.py',
        'simulator_config.ini'
    ]
    
    missing_files = []
    
    for filename in required_files:
        file_path = current_dir / filename
        if file_path.exists():
            print(f"✓ {filename}")
        else:
            print(f"✗ {filename} (missing)")
            missing_files.append(filename)
    
    if missing_files:
        print(f"\n⚠ {len(missing_files)} files are missing:")
        for filename in missing_files:
            print(f"  - {filename}")
        return False
    else:
        print("\n✓ All required files are present")
        return True

def check_permissions():
    """Check if files have proper permissions."""
    print("\n=== File Permissions Check ===")

    # Handle cases where __file__ is not defined
    try:
        current_dir = Path(__file__).parent
    except NameError:
        current_dir = Path.cwd()

    executable_files = [
        'run_simulator.py',
        'test_imports.py',
        'setup_environment.py'
    ]
    
    permission_issues = []
    
    for filename in executable_files:
        file_path = current_dir / filename
        if file_path.exists():
            if os.access(file_path, os.R_OK):
                print(f"✓ {filename} is readable")
            else:
                print(f"✗ {filename} is not readable")
                permission_issues.append(filename)
        else:
            print(f"⚠ {filename} does not exist")
    
    return len(permission_issues) == 0

def run_import_test():
    """Run the import test script."""
    print("\n=== Running Import Test ===")
    
    try:
        # Import the test function
        from test_imports import test_imports
        return test_imports()
    except Exception as e:
        print(f"✗ Failed to run import test: {e}")
        return False

def create_launcher_script():
    """Create a simple launcher script."""
    print("\n=== Creating Launcher Script ===")

    # Handle cases where __file__ is not defined
    try:
        current_dir = Path(__file__).parent
    except NameError:
        current_dir = Path.cwd()
    
    # Windows batch file
    if sys.platform == 'win32':
        launcher_path = current_dir / 'start_simulator.bat'
        launcher_content = f'''@echo off
cd /d "{current_dir}"
python run_simulator.py
pause
'''
        try:
            with open(launcher_path, 'w') as f:
                f.write(launcher_content)
            print(f"✓ Created Windows launcher: {launcher_path}")
        except Exception as e:
            print(f"✗ Failed to create Windows launcher: {e}")
    
    # Unix shell script
    else:
        launcher_path = current_dir / 'start_simulator.sh'
        launcher_content = f'''#!/bin/bash
cd "{current_dir}"
python3 run_simulator.py
'''
        try:
            with open(launcher_path, 'w') as f:
                f.write(launcher_content)
            os.chmod(launcher_path, 0o755)
            print(f"✓ Created Unix launcher: {launcher_path}")
        except Exception as e:
            print(f"✗ Failed to create Unix launcher: {e}")

def check_python_environment():
    """Check and display detailed Python environment information."""
    print("\n=== Python Environment Analysis ===")
    print(f"Python executable: {sys.executable}")
    print(f"Python version: {sys.version}")
    print(f"Platform: {sys.platform}")
    print(f"Current working directory: {Path.cwd()}")

    # Show script location if available
    try:
        script_dir = Path(__file__).parent
        print(f"Script directory: {script_dir}")
    except NameError:
        print("Script directory: __file__ not available (string execution)")

    print(f"\nPython sys.path ({len(sys.path)} entries):")
    for i, path in enumerate(sys.path):
        path_obj = Path(path)
        exists = "✓" if path_obj.exists() else "✗"
        is_dir = "DIR" if path_obj.is_dir() else "FILE" if path_obj.is_file() else "N/A"
        print(f"  {i:2d}: {exists} [{is_dir:4s}] {path}")

    # Check for common Python package locations
    print(f"\nCommon Python locations:")
    common_locations = [
        Path(sys.executable).parent,  # Python installation
        Path(sys.executable).parent / "Lib",  # Standard library
        Path(sys.executable).parent / "Lib" / "site-packages",  # Third-party packages
    ]

    for location in common_locations:
        exists = "✓" if location.exists() else "✗"
        print(f"  {exists} {location}")

    return True

def main():
    """Main setup function."""
    print("ARINC Flight Simulator Environment Setup")
    print("=" * 50)

    all_checks_passed = True
    
    # Run all checks
    checks = [
        check_python_version,
        check_required_packages,
        check_python_environment,
        check_file_structure,
        check_permissions
    ]
    
    for check_func in checks:
        if not check_func():
            all_checks_passed = False
    
    # Run import test
    if all_checks_passed:
        if not run_import_test():
            all_checks_passed = False
    
    # Create launcher if everything is OK
    if all_checks_passed:
        create_launcher_script()
    
    # Final summary
    print("\n" + "=" * 50)
    if all_checks_passed:
        print("🎉 Environment setup completed successfully!")
        print("\nYou can now run the simulator using:")
        print("  python run_simulator.py")
        if sys.platform == 'win32':
            print("  or double-click start_simulator.bat")
        else:
            print("  or ./start_simulator.sh")
    else:
        print("❌ Environment setup found issues that need to be resolved.")
        print("\nPlease fix the issues above and run this script again.")
    
    print("\nFor additional help, run: python test_imports.py")

if __name__ == "__main__":
    main()
