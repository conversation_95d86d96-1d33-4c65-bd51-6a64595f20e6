import tkinter as tk
from tkinter import ttk
from airports_data import get_airport_names_dict

def update_airport_dropdowns(*args):
    """Update airport dropdown lists based on current selections."""
    start_airport = start_var.get()
    dest_airport = dest_var.get()

    # No error messages needed with smart dropdown behavior

    # Update start airport dropdown
    start_values = list(airport_names)
    if dest_airport and dest_airport in start_values:
        # Move destination airport to end with special marking
        start_values.remove(dest_airport)
        start_values.append(f"{dest_airport} (currently destination)")
    start_combo['values'] = start_values

    # Update destination airport dropdown
    dest_values = list(airport_names)
    if start_airport and start_airport in dest_values:
        # Remove start airport from destination options
        dest_values.remove(start_airport)
    dest_combo['values'] = dest_values

def on_start_airport_selected(event):
    """Handle start airport selection."""
    selected = start_var.get()

    # Check if user selected the "currently destination" option
    if selected.endswith(" (currently destination)"):
        # Extract the actual airport name
        actual_airport = selected.replace(" (currently destination)", "")
        start_var.set(actual_airport)
        # Reset destination since user chose the same airport
        dest_var.set("")
        # Update dropdowns
        update_airport_dropdowns()

def on_dest_airport_selected(event):
    """Handle destination airport selection."""
    # Just update dropdowns when destination changes
    update_airport_dropdowns()

def create_ui():
    """Create and configure the UI components without starting the mainloop."""
    global root, start_var, dest_var, flight_var, daytime_var, name_to_icao
    global airport_names, start_combo, dest_combo

    root = tk.Tk()
    root.title("ARINC Flight Simulator")

    # Set fixed window size - reduced height by 1/4 (from 800 to 600)
    root.geometry("600x600")
    root.resizable(False, True)  # Fixed width, resizable height

    mainframe = ttk.Frame(root, padding="12 12 12 12")
    mainframe.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

    # Configure grid weights - only allow horizontal expansion of mainframe
    root.grid_rowconfigure(0, weight=0)  # Don't expand mainframe vertically
    root.grid_columnconfigure(0, weight=1)

    # Configure mainframe grid weights for better layout
    mainframe.grid_columnconfigure(1, weight=1)  # Make column 1 (input fields) expandable

    # Get airport ICAO codes and names from airports_data
    airport_names_dict = get_airport_names_dict()
    name_to_icao = {v: k for k, v in airport_names_dict.items()}
    airport_names = list(airport_names_dict.values())

    ttk.Label(mainframe, text="Start Airport:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
    start_var = tk.StringVar()
    start_combo = ttk.Combobox(mainframe, textvariable=start_var, values=airport_names, state="readonly")
    start_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))

    ttk.Label(mainframe, text="Destination Airport:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
    dest_var = tk.StringVar()
    dest_combo = ttk.Combobox(mainframe, textvariable=dest_var, values=airport_names, state="readonly")
    dest_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 5))

    ttk.Label(mainframe, text="Flight Number:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10))
    flight_var = tk.StringVar(value="LH1234")  # Preset flight number
    ttk.Entry(mainframe, textvariable=flight_var).grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(0, 5))

    ttk.Label(mainframe, text="Daytime at Start:").grid(row=3, column=0, sticky=tk.W, padx=(0, 10))
    daytime_var = tk.StringVar(value="12:00")  # Preset daytime
    ttk.Entry(mainframe, textvariable=daytime_var).grid(row=3, column=1, sticky=(tk.W, tk.E), padx=(0, 5))

    # Bind dropdown update functions
    start_var.trace_add('write', update_airport_dropdowns)
    dest_var.trace_add('write', update_airport_dropdowns)

    # Bind selection event handlers
    start_combo.bind('<<ComboboxSelected>>', on_start_airport_selected)
    dest_combo.bind('<<ComboboxSelected>>', on_dest_airport_selected)

    # Initial dropdown setup
    update_airport_dropdowns()

    return root

# Initialize variables as None - they will be set when create_ui() is called
root = None
start_var = None
dest_var = None
flight_var = None
daytime_var = None
name_to_icao = None
airport_names = None
start_combo = None
dest_combo = None

# Only run the UI directly if this file is executed as main
if __name__ == "__main__":
    create_ui()
    root.mainloop()
