import tkinter as tk
from tkinter import ttk
from tkinter import messagebox
from airports_data import get_airport_names_dict

def submit():
    # Map selected names back to ICAO codes
    start_name = start_var.get()
    dest_name = dest_var.get()
    start_icao = name_to_icao.get(start_name, "")
    dest_icao = name_to_icao.get(dest_name, "")
    flight = flight_var.get()
    daytime = daytime_var.get()
    info = (
        f"Start Airport: {start_icao} ({start_name})\n"
        f"Destination Airport: {dest_icao} ({dest_name})\n"
        f"Flight Number: {flight}\n"
        f"Daytime at Start: {daytime}"
    )
    messagebox.showinfo("Flight Information", info)

root = tk.Tk()
root.title("Flight Information Input")

mainframe = ttk.Frame(root, padding="12 12 12 12")
mainframe.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

# Get airport ICAO codes and names from airports_data
airport_names_dict = get_airport_names_dict()
icao_to_name = airport_names_dict
name_to_icao = {v: k for k, v in airport_names_dict.items()}
airport_names = list(airport_names_dict.values())

ttk.Label(mainframe, text="Start Airport:").grid(row=0, column=0, sticky=tk.W)
start_var = tk.StringVar()
start_combo = ttk.Combobox(mainframe, width=30, textvariable=start_var, values=airport_names, state="readonly")
start_combo.grid(row=0, column=1)

ttk.Label(mainframe, text="Destination Airport:").grid(row=1, column=0, sticky=tk.W)
dest_var = tk.StringVar()
dest_combo = ttk.Combobox(mainframe, width=30, textvariable=dest_var, values=airport_names, state="readonly")
dest_combo.grid(row=1, column=1)

ttk.Label(mainframe, text="Flight Number:").grid(row=2, column=0, sticky=tk.W)
flight_var = tk.StringVar(value="LH1234")  # Preset flight number
ttk.Entry(mainframe, width=20, textvariable=flight_var).grid(row=2, column=1)

ttk.Label(mainframe, text="Daytime at Start:").grid(row=3, column=0, sticky=tk.W)
daytime_var = tk.StringVar(value="12:00")  # Preset daytime
ttk.Entry(mainframe, width=20, textvariable=daytime_var).grid(row=3, column=1)

ttk.Button(mainframe, text="Submit", command=submit).grid(row=4, column=0, columnspan=2, pady=10)

root.mainloop()