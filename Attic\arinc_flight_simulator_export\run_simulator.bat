@echo off
REM ARINC Flight Simulator Launcher for Windows
REM Double-click this file to start the simulator

echo Starting ARINC Flight Simulator...
echo.

REM Try to run with python3 first, then python
python3 arinc_flight_simulator.py 2>nul
if %errorlevel% neq 0 (
    python arinc_flight_simulator.py 2>nul
    if %errorlevel% neq 0 (
        echo Error: Python not found or simulator failed to start
        echo.
        echo Please ensure:
        echo 1. Python 3.6+ is installed
        echo 2. All simulator files are in this directory
        echo 3. Python is in your system PATH
        echo.
        pause
        exit /b 1
    )
)

echo Simulator closed.
pause
