import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import os
from ui import root, start_var, dest_var, flight_var, daytime_var, name_to_icao
from flight_model import simulate_flight_data
from A429_utilities import encode_flight_info

class ArincFlightSimulator:
    def __init__(self):
        self.running = False
        self.flight_thread = None
        self.time_since_takeoff = 0
        self.random_seed = 123
        self.update_interval = 1.0  # seconds
        
        # Add simulation controls to the existing UI
        self.add_controls_to_ui()
    
    def add_controls_to_ui(self):
        # Add a frame for simulation controls
        control_frame = ttk.LabelFrame(root, text="Simulation Controls")
        control_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=10, pady=10)
        
        # Start button
        self.start_button = ttk.Button(control_frame, text="Start Simulation", command=self.start_simulation)
        self.start_button.grid(row=0, column=0, padx=5, pady=5)
        
        # Stop button
        self.stop_button = ttk.Button(control_frame, text="Stop Simulation", command=self.stop_simulation)
        self.stop_button.grid(row=0, column=1, padx=5, pady=5)
        self.stop_button.config(state=tk.DISABLED)
        
        # Status label
        self.status_var = tk.StringVar(value="Ready")
        ttk.Label(control_frame, textvariable=self.status_var).grid(row=0, column=2, padx=5, pady=5)
        
        # Progress bar
        self.progress_var = tk.DoubleVar(value=0.0)
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=1.0)
        self.progress_bar.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), padx=5, pady=5)
        
        # Output frame
        output_frame = ttk.LabelFrame(root, text="ARINC Data Output")
        output_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)
        
        # Text widget for output
        self.output_text = tk.Text(output_frame, height=15, width=60)
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(output_frame, orient=tk.VERTICAL, command=self.output_text.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.output_text.configure(yscrollcommand=scrollbar.set)
        
        # Configure grid weights
        root.grid_rowconfigure(6, weight=1)
        root.grid_columnconfigure(0, weight=1)
        output_frame.grid_rowconfigure(0, weight=1)
        output_frame.grid_columnconfigure(0, weight=1)
    
    def start_simulation(self):
        # Get input values
        start_airport_name = start_var.get()
        dest_airport_name = dest_var.get()
        flight_number = flight_var.get()
        daytime = daytime_var.get()
        
        # Convert airport names to ICAO codes
        start_icao = name_to_icao.get(start_airport_name, "")
        dest_icao = name_to_icao.get(dest_airport_name, "")
        
        if not start_icao or not dest_icao:
            messagebox.showerror("Error", "Please select valid airports")
            return
        
        # Initialize simulation
        self.time_since_takeoff = 0
        self.start_airport = start_icao
        self.dest_airport = dest_icao
        self.flight_number = flight_number
        self.city_pair = f"{start_icao[:3]}{dest_icao[:3]}"  # Create city pair from ICAO codes
        
        # Update UI
        self.status_var.set("Initializing...")
        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, f"Initializing simulation for flight {flight_number}\n")
        self.output_text.insert(tk.END, f"Route: {start_airport_name} ({start_icao}) → {dest_airport_name} ({dest_icao})\n")
        self.output_text.insert(tk.END, f"Departure time: {daytime}\n\n")
        self.output_text.insert(tk.END, "Setting up ARINC labels...\n")
        self.output_text.see(tk.END)
        
        # Simulate ARINC board initialization (similar to LSY_Arinc_TestSetup)
        self.output_text.insert(tk.END, "Initializing ARINC board...\n")
        self.output_text.insert(tk.END, "Setting up channels...\n")
        self.output_text.insert(tk.END, "Configuring labels...\n\n")
        self.output_text.see(tk.END)
        
        # Start simulation thread
        self.running = True
        self.flight_thread = threading.Thread(target=self.run_simulation)
        self.flight_thread.daemon = True
        self.flight_thread.start()
        
        # Update button states
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
    
    def stop_simulation(self):
        self.running = False
        if self.flight_thread:
            self.flight_thread.join(timeout=1.0)
        
        self.status_var.set("Stopped")
        self.output_text.insert(tk.END, "\nSimulation stopped\n")
        self.output_text.see(tk.END)
        
        # Update button states
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
    
    def run_simulation(self):
        self.status_var.set("Running")
        
        while self.running:
            # Get flight data
            flight_data = simulate_flight_data(
                self.start_airport, 
                self.dest_airport, 
                self.time_since_takeoff, 
                self.random_seed
            )
            
            # Update progress bar
            self.progress_var.set(flight_data["progress"])
            
            # Generate ARINC words for flight info
            arinc_words = encode_flight_info(self.flight_number, self.city_pair)
            
            # Generate ARINC words for flight parameters
            # This would simulate what LSY_Arinc_TestSetup does with its labels
            self.update_arinc_data(flight_data, arinc_words)
            
            # Increment simulation time
            self.time_since_takeoff += int(self.update_interval * 10)  # Accelerate time
            
            # Check if flight is complete
            if flight_data["progress"] >= 1.0:
                self.running = False
                self.status_var.set("Completed")
                self.output_text.insert(tk.END, "\nFlight completed\n")
                self.output_text.see(tk.END)
                self.start_button.config(state=tk.NORMAL)
                self.stop_button.config(state=tk.DISABLED)
                break
            
            time.sleep(self.update_interval)
    
    def update_arinc_data(self, flight_data, arinc_words):
        # Clear output
        self.output_text.delete(1.0, tk.END)
        
        # Display flight status
        self.output_text.insert(tk.END, f"Flight: {self.flight_number} ({self.city_pair})\n")
        self.output_text.insert(tk.END, f"Phase: {flight_data['Flight_Phase']}\n")
        self.output_text.insert(tk.END, f"Progress: {flight_data['progress']*100:.1f}%\n")
        self.output_text.insert(tk.END, f"ETA: {flight_data['ETA_seconds']//60} minutes\n\n")
        
        # Display ARINC data that would be sent to the board
        self.output_text.insert(tk.END, "ARINC 429 Data Words:\n")
        self.output_text.insert(tk.END, "-" * 50 + "\n")
        
        # Flight info words
        for key, value in arinc_words.items():
            self.output_text.insert(tk.END, f"{key}: {value:06X}\n")
        
        # Flight parameter words
        self.output_text.insert(tk.END, "-" * 50 + "\n")
        self.output_text.insert(tk.END, "Flight Parameters:\n")
        
        # Map flight data to ARINC parameters (similar to AllParams in LSY_Arinc_TestSetup)
        arinc_params = {
            "Present-Position---Latitude": flight_data["Latitude_of_aircraft"],
            "Present-Position---Longitude": flight_data["Longitude_of_aircraft"],
            "Ground-Speed": flight_data["Ground_Speed_knots"],
            "True-Heading": flight_data["Heading_degrees"],
            "Baro-Corrected-Altitude1": flight_data["Altitude_of_aircraft_ft"],
            "Static-Air-Temperature": 15.0 - (flight_data["Altitude_of_aircraft_ft"] / 1000) * 2  # Simple temp model
        }
        
        # Display the parameters that would be sent to ARINC labels
        for param_name, value in arinc_params.items():
            # Simulate ARINC word encoding (simplified)
            if "Latitude" in param_name or "Longitude" in param_name:
                # Encode as BNR format
                encoded_value = int(abs(value) * 1e5) & 0x7FFFFF
                if value < 0:
                    encoded_value |= 0x800000  # Set sign bit
            elif "Altitude" in param_name:
                # Encode as BNR format
                encoded_value = int(value) & 0xFFFFFF
            else:
                # Simple encoding for other values
                encoded_value = int(value * 128) & 0xFFFFFF
            
            self.output_text.insert(tk.END, f"{param_name}: {value:.6f} → {encoded_value:06X}\n")
        
        self.output_text.see(tk.END)

# Create and run the application
if __name__ == "__main__":
    simulator = ArincFlightSimulator()
    root.mainloop()