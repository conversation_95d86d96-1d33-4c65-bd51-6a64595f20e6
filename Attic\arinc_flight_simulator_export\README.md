# ARINC Flight Simulator

A Python-based flight simulator that generates ARINC 429 data for aviation applications. This simulator provides a graphical user interface for configuring flight parameters and displays real-time ARINC 429 encoded flight data.

## Features

- **Interactive GUI**: Easy-to-use interface with optimized layout (600x600 window, fixed width, expandable output area)
- **Real-time Simulation**: Live flight data generation with progress tracking
- **ARINC 429 Encoding**: Proper encoding of flight parameters into ARINC 429 format
- **PbaPro Integration**: Automatic detection and integration with PbaPro ARINC system
- **Dual Mode Operation**: Works with or without PbaPro hardware
- **Multiple Airports**: Support for major international airports
- **Flight Phases**: Realistic simulation of climb, cruise, and descent phases
- **Progress Tracking**: Visual progress bar and ETA calculations
- **Label Management**: Direct assignment of flight parameters to ARINC labels
- **Configuration System**: Comprehensive configuration with UI editor
- **Flexible Setup**: Configurable channels, labels, timing, and PbaPro settings

## Requirements

- Python 3.6 or higher
- No external dependencies (uses only Python standard library)
- **Optional**: PbaPro system for actual ARINC hardware integration

## Installation

1. Ensure Python 3.6+ is installed on your system
2. Download or clone this folder
3. No additional packages need to be installed (all dependencies are part of Python standard library)
4. **Optional**: Run the environment setup script to verify everything is working:
   ```bash
   python setup_environment.py
   ```

## Usage

### Quick Start

**Option 1: Environment Setup (Recommended for first-time users)**
```bash
python setup_environment.py
```
This will check your environment and create launcher scripts.

**Option 2: Test Imports (If having import issues)**
```bash
python test_imports.py
```
This will diagnose and help fix import problems.

**Option 3: Detailed Path Diagnostics**
```bash
python diagnose_paths.py
```
This provides comprehensive analysis of Python's package search paths.

### Running the Simulator

**Option 1: Using the launcher script (recommended)**
```bash
python run_simulator.py
```

**Option 2: Direct execution**
```bash
python arinc_flight_simulator.py
```

**Option 3: Platform-specific launchers**
- Windows: Double-click `run_simulator.bat` or `start_simulator.bat`
- Linux/Mac: Run `./start_simulator.sh` (created by setup script)

### Using the Interface

1. **Select Start Airport**: Choose departure airport from the dropdown
2. **Select Destination Airport**: Choose arrival airport from the dropdown (must be different from start)
3. **Enter Flight Number**: Input your flight identifier (default: LH1234)
4. **Set Departure Time**: Enter departure time (default: 12:00)
5. **Configure Settings**: Click "Configuration" to adjust simulator settings (only when not running)
6. **Start Simulation**: Click "Start Simulation" to begin
7. **Monitor Progress**: Watch the progress bar and ARINC data output
8. **Stop if Needed**: Use "Stop Simulation" to halt the simulation

### Smart Airport Selection

The simulator includes intelligent airport selection:
- **Conflict Prevention**: Selected start airport is automatically removed from destination options
- **Smart Reset**: Selecting the destination airport as start automatically clears destination
- **Visual Indicators**: Currently selected airports are marked in dropdowns
- **No Error Messages**: Elegant prevention instead of error notifications

### ARINC Data Output

The simulator displays two types of data:

**1. Encoded Flight Information (ARINC 429 Words):**
- Flight number (encoded using Char 2 format)
- City pair (encoded using Char 3 format)
- Displayed as hexadecimal values

**2. Flight Parameters (Direct Label Values):**
- Present Position (Latitude/Longitude) - direct assignment
- Ground Speed - direct assignment
- True Heading - direct assignment
- Barometric Altitude - direct assignment
- Static Air Temperature - direct assignment
- Displayed as decimal values (no encoding)
- **Automatically sent to PbaPro labels if available**

## File Structure

```
arinc_flight_simulator_export/
├── arinc_flight_simulator.py  # Main simulator application
├── ui.py                      # User interface components
├── flight_model.py           # Flight simulation logic
├── A429_utilities.py         # ARINC 429 encoding utilities
├── airports_data.py          # Airport database
├── config_manager.py         # Configuration system
├── import_utils.py           # Import utilities for robust module loading
├── run_simulator.py          # Launcher script (recommended)
├── test_imports.py           # Import testing and diagnostics
├── diagnose_paths.py         # Detailed Python path analysis
├── find_and_run.py           # Universal launcher (finds simulator from anywhere)
├── setup_environment.py     # Environment setup and validation
├── simulator_config.ini      # Configuration file
├── requirements.txt          # Python dependencies (none required)
├── README.md                # This documentation
├── __init__.py              # Python package initialization
└── Combined.ppdbs           # Optional: PbaPro database file
```

## Supported Airports

The simulator includes the following airports:
- EDDF - Frankfurt am Main Airport (Germany)
- KJFK - John F. Kennedy International Airport (USA)
- EGLL - Heathrow Airport (United Kingdom)
- EHAM - Amsterdam Airport Schiphol (Netherlands)
- LFPG - Charles de Gaulle Airport (France)
- RJTT - Tokyo Haneda Airport (Japan)
- YSSY - Sydney Kingsford Smith Airport (Australia)
- FAOR - O. R. Tambo International Airport (South Africa)
- CYYZ - Toronto Pearson International Airport (Canada)
- OMDB - Dubai International Airport (UAE)
- ZBAA - Beijing Capital International Airport (China)
- LOWL - Linz Airport (Austria)

## Technical Details

### ARINC 429 Data Handling

The simulator implements two different approaches:

**Encoded Data (ARINC 429 Words):**
- **Char 2 Format**: For flight numbers (2 characters per word)
- **Char 3 Format**: For city pairs (3 characters per word)
- Output as hexadecimal ARINC 429 words

**Direct Label Assignment:**
- Flight parameters are assigned directly to label values
- No encoding applied - raw decimal values
- Suitable for direct hardware label assignment

### Channel Configuration

The simulator uses a hardcoded channel configuration based on LSY_Arinc_TestSetup:

**Channel Setup:**
- Channel 1: 12.5 kHz (Position & Navigation data)
- Channel 2: 12.5 kHz (Altitude & Temperature data)
- Channel 3: Low speed (Reserved)
- Channel 4: Low speed (Reserved)

**Parameter Distribution:**
- **Channel 1**: Latitude, Longitude, Ground Speed, True Heading
- **Channel 2**: Barometric Altitude, Static Air Temperature
- Label numbers determined from Combined.ppdbs database or fallback numbering

## Configuration System

The simulator includes a comprehensive configuration system accessible via the "Configuration" button.

### Configuration Options

**General Settings:**
- **Time Acceleration**: Speed up simulation (1-100x real time)
- **Update Interval**: How often to update display (0.1-10.0 seconds)
- **PbaPro Board Name**: Name of the PbaPro board resource
- **Random Seed**: Seed for flight data generation (1-999999) - controls flight path variations and turbulence

**Channel Configuration:**
- **Enable/Disable**: Individual channel control
- **Speed Settings**: 12.5 kHz, 100 kHz, Low, High for each channel
- **4 Channels Available**: Channels 1-4 with independent settings

**Label Mapping:**
- **Parameter Assignment**: Which channel each flight parameter uses
- **Label Numbers**: Automatic from database or manual specification
- **Flexible Routing**: Any parameter can use any enabled channel

### Configuration File

Settings are stored in `simulator_config.ini`:
- Automatically created with defaults if missing
- Human-readable INI format
- Can be edited manually or via UI
- Validated on load and save

### Using the Configuration Dialog

1. Click "Configuration" button (only available when simulation stopped)
2. Use tabs to navigate: General, Channels, Labels
3. Modify settings as needed
4. Click "OK" to save or "Cancel" to discard
5. "Reset to Defaults" restores factory settings
6. Input validation prevents invalid configurations

### PbaPro Integration

The simulator automatically detects and integrates with PbaPro systems:

**With PbaPro Available:**
- Initializes ARINC 429 board resources
- Configures channels with proper speeds (Channel 1&2: 12.5, Channel 3&4: Low)
- Loads Combined.ppdbs database file (if available)
- Creates labels for flight parameters on appropriate channels
- Sends real-time data to PbaPro database
- Status shown as "PbaPro: Connected" (green)

**Without PbaPro:**
- Runs in simulation-only mode
- All data displayed in UI only
- Status shown as "PbaPro: Simulation Only" (orange)
- No functionality loss - full simulation capabilities

### Flight Model

The flight simulation includes:
- **Realistic Flight Phases**: Climb (15%), Cruise (70%), Descent (15%)
- **Great Circle Navigation**: Accurate distance and bearing calculations
- **Dynamic Parameters**: Speed, altitude, and heading variations
- **Weather Simulation**: Basic temperature modeling

## Customization

### Adding New Airports

Edit `airports_data.py` and add new entries to the `AIRPORTS` dictionary:

```python
"ICAO": {
    "name": "Airport Name",
    "city": "City",
    "country": "Country",
    "latitude": 00.0000,
    "longitude": 00.0000,
    "elevation_ft": 0000,
    "runways": [...]
}
```

### Modifying ARINC Parameters

Edit the `arinc_params` dictionary in `arinc_flight_simulator.py` to add or modify ARINC parameters.

## Troubleshooting

### Import Issues

If you encounter `ModuleNotFoundError: No module named 'ui'` or similar import errors:

1. **Run the diagnostic tools**:
   ```bash
   python diagnose_paths.py    # Detailed path analysis
   python test_imports.py      # Import testing
   ```
   These will identify and help fix import problems.

2. **Use the universal launcher**:
   ```bash
   python find_and_run.py      # Finds and runs simulator from anywhere
   ```
   This can locate and run the simulator from any directory.

3. **Use the environment setup**:
   ```bash
   python setup_environment.py
   ```
   This will check your environment and fix common issues.

4. **Manual troubleshooting**:
   - Ensure all files are in the same directory
   - Check that you're running Python from the correct directory
   - Verify all required files are present (see File Structure above)

### Common Issues

1. **Import Errors**: Use `test_imports.py` to diagnose and fix
2. **GUI Not Appearing**: Check that tkinter is available (included with most Python installations)
3. **Airport Not Found**: Verify airport ICAO codes in airports_data.py
4. **Permission Issues**: On Linux/Mac, ensure scripts have execute permissions

### Error Messages

- "Please select valid airports": Choose airports from the dropdown menus
- "Start airport not found": Airport ICAO code missing from database
- "Destination airport not found": Airport ICAO code missing from database
- "Failed to import required modules": Run `python test_imports.py` for diagnosis

### Platform-Specific Notes

**Windows:**
- Use `python` command
- Batch files (.bat) should work by double-clicking
- If Python is not in PATH, use full path to python.exe

**Linux/Mac:**
- Use `python3` command if `python` points to Python 2
- Shell scripts need execute permission: `chmod +x start_simulator.sh`
- On some Linux distributions, install tkinter separately: `sudo apt-get install python3-tk`

## License

This project is provided as-is for educational and development purposes.

## Contributing

To contribute improvements:
1. Fork the project
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

For questions or issues, please refer to the code comments or create an issue in the project repository.
