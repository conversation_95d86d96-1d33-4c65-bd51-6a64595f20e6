"""
Configuration Management Module

This module handles configuration loading, saving, and validation for the ARINC Flight Simulator.
"""

import configparser
import os
from typing import Dict, List, Tuple, Any
import tkinter as tk
from tkinter import ttk, messagebox


class ConfigManager:
    """Manages configuration settings for the ARINC Flight Simulator."""

    # Mapping between simulated values and user-friendly names
    SIMULATED_VALUES = {
        'Latitude': 'Latitude_of_aircraft',
        'Longitude': 'Longitude_of_aircraft',
        'Ground_Speed': 'Ground_Speed_knots',
        'Heading': 'Heading_degrees',
        'Altitude': 'Altitude_of_aircraft_ft',
        'IAS': 'Indicated_Air_Speed_knots',
        'Phase': 'Flight_Phase',
        'ETA': 'ETA_seconds',
        'Flight_Time': 'Total_Flight_Time_seconds',
        'Distance': 'Distance_nm',
        'Progress': 'progress',
        'Temperature': 'Static_Air_Temperature',  # Calculated value
        'CityPair0': 'CityPair0',  # Encoded flight info
        'CityPair1': 'CityPair1',  # Encoded flight info
        'FlightNr0': 'FlightNr0',  # Encoded flight info
        'FlightNr1': 'FlightNr1',  # Encoded flight info
        'FlightNr2': 'FlightNr2'   # Encoded flight info
    }

    # Default label configuration (label_name: (channel, label_id, simulated_value))
    DEFAULT_LABELS = {
        'Present-Position---Latitude': (1, 'x', 'Latitude'),
        'Present-Position---Longitude': (1, 'x', 'Longitude'),
        'Ground-Speed': (1, 'x', 'Ground_Speed'),
        'True-Heading': (1, 'x', 'Heading'),
        'Baro-Corrected-Altitude1': (2, 'x', 'Altitude'),
        'Static-Air-Temperature': (2, 'x', 'Temperature'),
        'ACMS-Information-40': (3, 'x', 'CityPair0'),
        'ACMS-Information-41': (3, 'x', 'CityPair1'),
        'ACMS-Information-42': (3, 'x', 'FlightNr0'),
        'ACMS-Information-233': (4, 'x', 'FlightNr1'),
        'ACMS-Information-234': (4, 'x', 'FlightNr2'),
        'ACMS-Information-235': (4, 'x', 'FlightNr0'),
        'ACMS-Information-236': (4, 'x', 'FlightNr1')
    }

    @classmethod
    def get_simulated_value_names(cls):
        """Get list of user-friendly simulated value names."""
        return list(cls.SIMULATED_VALUES.keys())

    @classmethod
    def get_flight_data_key(cls, simulated_value_name):
        """Get the flight data key for a simulated value name."""
        return cls.SIMULATED_VALUES.get(simulated_value_name, simulated_value_name)

    def __init__(self, config_file: str = "simulator_config.ini"):
        """Initialize the configuration manager."""
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        # Preserve case for option names (by default ConfigParser converts to lowercase)
        self.config.optionxform = str

        # Default configuration values
        self.defaults = {
            'GENERAL': {
                'time_acceleration': '10',
                'update_interval': '1.0',
                'pbapro_board_name': 'ResourceList.A429-Board2',
                'random_seed': '123',
                'movement_margin': '1.0'
            },
            'CHANNELS': {
                'channel_1_speed': 'Low',
                'channel_2_speed': 'Low',
                'channel_3_speed': 'Low',
                'channel_4_speed': 'Low',
                'channel_1_enabled': 'True',
                'channel_2_enabled': 'True',
                'channel_3_enabled': 'True',
                'channel_4_enabled': 'True'
            },
            'LABELS': {}  # Will be populated from DEFAULT_LABELS
        }

        # Populate LABELS from DEFAULT_LABELS
        for label_name, (channel, label_id, simulated_value) in self.DEFAULT_LABELS.items():
            self.defaults['LABELS'][label_name] = f"{channel},{label_id},{simulated_value}"
        
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from file or create with defaults."""
        if os.path.exists(self.config_file):
            try:
                self.config.read(self.config_file)
                print(f"Configuration loaded from {self.config_file}")
            except Exception as e:
                print(f"Error loading config file: {e}")
                self.create_default_config()
        else:
            print(f"Config file {self.config_file} not found, creating with defaults")
            self.create_default_config()
    
    def create_default_config(self) -> None:
        """Create configuration file with default values."""
        for section, options in self.defaults.items():
            self.config.add_section(section)
            for key, value in options.items():
                self.config.set(section, key, value)
        self.save_config()
    
    def save_config(self) -> bool:
        """Save current configuration to file."""
        try:
            with open(self.config_file, 'w') as f:
                self.config.write(f)
            print(f"Configuration saved to {self.config_file}")
            return True
        except Exception as e:
            print(f"Error saving config file: {e}")
            return False
    
    def get_value(self, section: str, key: str, fallback: str = None) -> str:
        """Get configuration value with fallback."""
        try:
            value = self.config.get(section, key)
            if value is not None and value.strip():
                return value
        except:
            pass

        # Use fallback or default
        if fallback is not None:
            return fallback
        return self.defaults.get(section, {}).get(key, '')
    
    def get_int(self, section: str, key: str, fallback: int = None) -> int:
        """Get integer configuration value."""
        try:
            return self.config.getint(section, key)
        except:
            if fallback is not None:
                return fallback
            default_str = self.defaults.get(section, {}).get(key, '0')
            return int(default_str)
    
    def get_float(self, section: str, key: str, fallback: float = None) -> float:
        """Get float configuration value."""
        try:
            return self.config.getfloat(section, key)
        except:
            if fallback is not None:
                return fallback
            default_str = self.defaults.get(section, {}).get(key, '0.0')
            return float(default_str)

    def get_boolean(self, section: str, key: str, fallback: bool = None) -> bool:
        """Get boolean configuration value."""
        try:
            value = self.config.getboolean(section, key)
            return value
        except:
            if fallback is not None:
                return fallback
            default_str = self.defaults.get(section, {}).get(key, 'False')
            return default_str.lower() == 'true'
    
    def set_value(self, section: str, key: str, value: Any) -> None:
        """Set configuration value."""
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, key, str(value))
    
    def get_channel_config(self) -> Dict[int, str]:
        """Get channel configuration as dictionary."""
        channels = {}
        for i in range(1, 5):
            if self.get_boolean('CHANNELS', f'channel_{i}_enabled'):
                speed = self.get_value('CHANNELS', f'channel_{i}_speed')
                channels[i] = speed
        return channels
    
    def get_label_mapping(self) -> Dict[str, Tuple[int, str, str]]:
        """Get label mapping configuration."""
        mapping = {}

        # Get all labels from the LABELS section
        if self.config.has_section('LABELS'):
            for label_name in self.config.options('LABELS'):
                value = self.config.get('LABELS', label_name)
                if ',' in value:
                    parts = value.split(',')
                    try:
                        channel_no = int(parts[0])
                        label_string = parts[1].strip()
                        # Get simulated value (default to 'Latitude' if not specified for backward compatibility)
                        simulated_value = parts[2].strip() if len(parts) > 2 else 'Latitude'
                        # Use the original case from the config file
                        mapping[label_name] = (channel_no, label_string, simulated_value)
                    except (ValueError, IndexError):
                        print(f"Warning: Invalid label configuration for {label_name}: {value}")

        return mapping
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        # Validate time acceleration
        try:
            accel = self.get_int('GENERAL', 'time_acceleration')
            if accel < 1 or accel > 100:
                errors.append("Time acceleration must be between 1 and 100")
        except:
            errors.append("Time acceleration must be a valid integer")

        # Validate update interval
        try:
            interval = self.get_float('GENERAL', 'update_interval')
            if interval < 0.1 or interval > 10.0:
                errors.append("Update interval must be between 0.1 and 10.0 seconds")
        except:
            errors.append("Update interval must be a valid number")

        # Validate random seed
        try:
            seed = self.get_int('GENERAL', 'random_seed')
            if seed < 1 or seed > 999999:
                errors.append("Random seed must be between 1 and 999999")
        except:
            errors.append("Random seed must be a valid integer")

        # Validate movement margin
        try:
            margin = self.get_float('GENERAL', 'movement_margin')
            if margin < 0.0 or margin > 10.0:
                errors.append("Movement margin must be between 0.0 and 10.0")
        except:
            errors.append("Movement margin must be a valid number")
        
        # Validate channel speeds
        valid_speeds = ['Low', 'High']
        for i in range(1, 5):
            speed = self.get_value('CHANNELS', f'channel_{i}_speed')
            if speed not in valid_speeds:
                errors.append(f"Channel {i} speed must be one of: {', '.join(valid_speeds)}")
        
        return errors


class ConfigDialog:
    """Configuration dialog for the ARINC Flight Simulator."""
    
    def __init__(self, parent: tk.Tk, config_manager: ConfigManager):
        """Initialize the configuration dialog."""
        self.parent = parent
        self.config_manager = config_manager
        self.dialog = None
        self.result = False
        
    def show_dialog(self) -> bool:
        """Show the configuration dialog and return True if settings were saved."""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("ARINC Flight Simulator - Configuration")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        self.dialog.minsize(550, 450)
        
        # Make dialog modal
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Create notebook for tabs
        notebook = ttk.Notebook(self.dialog)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # General settings tab
        general_frame = ttk.Frame(notebook)
        notebook.add(general_frame, text="General")
        self.create_general_tab(general_frame)
        
        # Channels tab
        channels_frame = ttk.Frame(notebook)
        notebook.add(channels_frame, text="Channels")
        self.create_channels_tab(channels_frame)

        # Labels tab
        labels_frame = ttk.Frame(notebook)
        notebook.add(labels_frame, text="Labels")
        self.create_labels_tab(labels_frame)
        
        # Separator line
        separator = ttk.Separator(self.dialog, orient='horizontal')
        separator.pack(fill=tk.X, padx=10, pady=(10, 0))

        # Buttons frame at bottom
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        # Add some spacing to push buttons to the right
        spacer = ttk.Frame(button_frame)
        spacer.pack(side=tk.LEFT, expand=True)

        # Cancel and Save buttons with better styling
        cancel_btn = ttk.Button(button_frame, text="Cancel", command=self.cancel, width=12)
        cancel_btn.pack(side=tk.RIGHT, padx=(0, 10))

        save_btn = ttk.Button(button_frame, text="Save", command=self.save_config, width=12)
        save_btn.pack(side=tk.RIGHT)

        # Add tooltips to buttons
        self.create_tooltip(cancel_btn, "Cancel changes and close dialog")
        self.create_tooltip(save_btn, "Save configuration and apply changes")

        # Make Save button the default
        save_btn.focus_set()
        self.dialog.bind('<Return>', lambda e: self.save_config())
        self.dialog.bind('<Escape>', lambda e: self.cancel())
        
        # Center dialog on parent
        self.dialog.update_idletasks()
        x = (self.parent.winfo_x() + (self.parent.winfo_width() // 2) - 
             (self.dialog.winfo_width() // 2))
        y = (self.parent.winfo_y() + (self.parent.winfo_height() // 2) - 
             (self.dialog.winfo_height() // 2))
        self.dialog.geometry(f"+{x}+{y}")
        
        # Wait for dialog to close
        self.dialog.wait_window()
        
        return self.result

    def create_tooltip(self, widget, text):
        """Create a tooltip for a widget."""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
            label = ttk.Label(tooltip, text=text, background="lightyellow",
                            relief="solid", borderwidth=1, font=("TkDefaultFont", 8))
            label.pack()
            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)
    
    def create_general_tab(self, parent: ttk.Frame) -> None:
        """Create the general settings tab."""
        # Create a scrollable frame for better organization
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Simulation Settings
        sim_frame = ttk.LabelFrame(scrollable_frame, text="Simulation Settings")
        sim_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=5, pady=5)

        ttk.Label(sim_frame, text="Time Acceleration:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.time_accel_var = tk.StringVar(value=str(self.config_manager.get_int('GENERAL', 'time_acceleration')))
        time_accel_entry = ttk.Entry(sim_frame, textvariable=self.time_accel_var, width=15)
        time_accel_entry.grid(row=0, column=1, padx=5, pady=2)
        ttk.Label(sim_frame, text="(1-100, higher = faster simulation)").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)

        ttk.Label(sim_frame, text="Update Interval (s):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.update_interval_var = tk.StringVar(value=str(self.config_manager.get_float('GENERAL', 'update_interval')))
        interval_entry = ttk.Entry(sim_frame, textvariable=self.update_interval_var, width=15)
        interval_entry.grid(row=1, column=1, padx=5, pady=2)
        ttk.Label(sim_frame, text="(0.1-10.0, how often data updates)").grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)

        ttk.Label(sim_frame, text="Random Seed:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.random_seed_var = tk.StringVar(value=str(self.config_manager.get_int('GENERAL', 'random_seed')))
        seed_entry = ttk.Entry(sim_frame, textvariable=self.random_seed_var, width=15)
        seed_entry.grid(row=2, column=1, padx=5, pady=2)
        ttk.Label(sim_frame, text="(1-999999, for reproducible simulations)").grid(row=2, column=2, sticky=tk.W, padx=5, pady=2)

        ttk.Label(sim_frame, text="Movement Margin:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.movement_margin_var = tk.StringVar(value=str(self.config_manager.get_float('GENERAL', 'movement_margin')))
        margin_entry = ttk.Entry(sim_frame, textvariable=self.movement_margin_var, width=15)
        margin_entry.grid(row=3, column=1, padx=5, pady=2)
        ttk.Label(sim_frame, text="(0.0-10.0, data fluctuation amount)").grid(row=3, column=2, sticky=tk.W, padx=5, pady=2)

        # PbaPro Settings
        pbapro_frame = ttk.LabelFrame(scrollable_frame, text="PbaPro Settings")
        pbapro_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=5, pady=5)

        ttk.Label(pbapro_frame, text="Board Name:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.pbapro_board_var = tk.StringVar(value=self.config_manager.get_value('GENERAL', 'pbapro_board_name'))
        board_entry = ttk.Entry(pbapro_frame, textvariable=self.pbapro_board_var, width=30)
        board_entry.grid(row=0, column=1, padx=5, pady=2)
        ttk.Label(pbapro_frame, text="(PbaPro resource name)").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)

        # Pack the canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_channels_tab(self, parent: ttk.Frame) -> None:
        """Create the channels settings tab."""
        # Channel configuration frame
        channels_frame = ttk.LabelFrame(parent, text="ARINC 429 Channel Configuration")
        channels_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Headers
        ttk.Label(channels_frame, text="Channel", font=("TkDefaultFont", 9, "bold")).grid(row=0, column=0, padx=5, pady=5)
        ttk.Label(channels_frame, text="Enabled", font=("TkDefaultFont", 9, "bold")).grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(channels_frame, text="Speed", font=("TkDefaultFont", 9, "bold")).grid(row=0, column=2, padx=5, pady=5)

        # Channel variables
        self.channel_enabled_vars = {}
        self.channel_speed_vars = {}

        # Speed options
        speed_options = ["Low", "High"]

        # Create channel configuration rows
        for i in range(1, 5):
            row = i

            # Channel number
            ttk.Label(channels_frame, text=f"Channel {i}").grid(row=row, column=0, padx=5, pady=2)

            # Enabled checkbox
            enabled_var = tk.BooleanVar(value=self.config_manager.get_boolean('CHANNELS', f'channel_{i}_enabled'))
            self.channel_enabled_vars[i] = enabled_var
            ttk.Checkbutton(channels_frame, variable=enabled_var).grid(row=row, column=1, padx=5, pady=2)

            # Speed combobox
            speed_var = tk.StringVar(value=self.config_manager.get_value('CHANNELS', f'channel_{i}_speed'))
            self.channel_speed_vars[i] = speed_var
            speed_combo = ttk.Combobox(channels_frame, textvariable=speed_var, values=speed_options,
                                     state="readonly", width=10)
            speed_combo.grid(row=row, column=2, padx=5, pady=2)

        # Help text
        help_frame = ttk.Frame(parent)
        help_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        help_text = "Speed values: Low/High"
        ttk.Label(help_frame, text=help_text, font=("TkDefaultFont", 8), foreground="gray").pack()

    def create_labels_tab(self, parent: ttk.Frame) -> None:
        """Create the labels configuration tab."""
        # Main frame with scrollbar
        main_frame = ttk.Frame(parent)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Labels frame
        labels_frame = ttk.LabelFrame(main_frame, text="Label Configuration")
        labels_frame.pack(fill=tk.BOTH, expand=True)

        # Create scrollable area
        canvas = tk.Canvas(labels_frame)
        scrollbar = ttk.Scrollbar(labels_frame, orient="vertical", command=canvas.yview)
        self.scrollable_labels_frame = ttk.Frame(canvas)

        self.scrollable_labels_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_labels_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Configure column weights for proper alignment
        self.scrollable_labels_frame.grid_columnconfigure(0, weight=1)  # Label Name column expands
        self.scrollable_labels_frame.grid_columnconfigure(1, weight=0)  # Channel column fixed
        self.scrollable_labels_frame.grid_columnconfigure(2, weight=0)  # Label ID column fixed
        self.scrollable_labels_frame.grid_columnconfigure(3, weight=0)  # Simulated Value column fixed
        self.scrollable_labels_frame.grid_columnconfigure(4, weight=0)  # Actions column fixed

        # Headers - use same grid structure as data rows
        ttk.Label(self.scrollable_labels_frame, text="Label Name", font=("TkDefaultFont", 9, "bold")).grid(
            row=0, column=0, padx=5, pady=(5, 2), sticky=tk.W)
        ttk.Label(self.scrollable_labels_frame, text="Channel", font=("TkDefaultFont", 9, "bold")).grid(
            row=0, column=1, padx=5, pady=(5, 2))
        ttk.Label(self.scrollable_labels_frame, text="Label ID", font=("TkDefaultFont", 9, "bold")).grid(
            row=0, column=2, padx=5, pady=(5, 2))
        ttk.Label(self.scrollable_labels_frame, text="Simulated Value", font=("TkDefaultFont", 9, "bold")).grid(
            row=0, column=3, padx=5, pady=(5, 2))
        ttk.Label(self.scrollable_labels_frame, text="Actions", font=("TkDefaultFont", 9, "bold")).grid(
            row=0, column=4, padx=5, pady=(5, 2))

        # Add a visual separator line below headers
        separator = ttk.Separator(self.scrollable_labels_frame, orient='horizontal')
        separator.grid(row=1, column=0, columnspan=5, sticky=(tk.W, tk.E), padx=5, pady=(0, 5))

        # Label variables
        self.label_name_vars = {}
        self.label_channel_vars = {}
        self.label_id_vars = {}
        self.label_simulated_vars = {}
        self.label_rows = {}

        # Load existing labels
        self.load_labels()

        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(buttons_frame, text="Add Label", command=self.add_label).pack(side=tk.LEFT, padx=(0, 5))

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Help text
        help_frame = ttk.Frame(main_frame)
        help_frame.pack(fill=tk.X, pady=(10, 0))

        help_text = "Label ID: Use 'x' for database lookup, or octal number (e.g., '310' for label 310₈)\nSimulated Value: Choose which flight parameter to map to this label"
        ttk.Label(help_frame, text=help_text, font=("TkDefaultFont", 8), foreground="gray").pack()

    def load_labels(self):
        """Load existing labels from configuration."""
        # Clear existing data rows (keep headers and separator)
        for widget in self.scrollable_labels_frame.winfo_children():
            if widget.grid_info().get('row', 0) > 1:  # Keep header (row 0) and separator (row 1)
                widget.destroy()

        self.label_name_vars.clear()
        self.label_channel_vars.clear()
        self.label_id_vars.clear()
        self.label_simulated_vars.clear()
        self.label_rows.clear()

        # Get current label mapping
        label_mapping = self.config_manager.get_label_mapping()

        # Create rows for existing labels (start from row 2 after separator)
        row = 2
        for label_name, (channel, label_id, simulated_value) in label_mapping.items():
            self.create_label_row(row, label_name, channel, label_id, simulated_value)
            row += 1

    def create_label_row(self, row: int, label_name: str, channel: int, label_id: str, simulated_value: str = 'Latitude'):
        """Create a row for label configuration."""
        # Label name entry - fills available space
        name_var = tk.StringVar(value=label_name)
        self.label_name_vars[row] = name_var
        name_entry = ttk.Entry(self.scrollable_labels_frame, textvariable=name_var, width=25)
        name_entry.grid(row=row, column=0, padx=5, pady=2, sticky=(tk.W, tk.E))

        # Channel combobox - fixed width, centered
        channel_var = tk.StringVar(value=str(channel))
        self.label_channel_vars[row] = channel_var
        channel_combo = ttk.Combobox(self.scrollable_labels_frame, textvariable=channel_var,
                                   values=["1", "2", "3", "4"], state="readonly", width=8)
        channel_combo.grid(row=row, column=1, padx=5, pady=2)

        # Label ID entry - fixed width, centered
        id_var = tk.StringVar(value=label_id)
        self.label_id_vars[row] = id_var
        id_entry = ttk.Entry(self.scrollable_labels_frame, textvariable=id_var, width=10)
        id_entry.grid(row=row, column=2, padx=5, pady=2)

        # Simulated Value combobox - fixed width, centered
        simulated_var = tk.StringVar(value=simulated_value)
        self.label_simulated_vars[row] = simulated_var
        simulated_combo = ttk.Combobox(self.scrollable_labels_frame, textvariable=simulated_var,
                                     values=self.config_manager.get_simulated_value_names(),
                                     state="readonly", width=12)
        simulated_combo.grid(row=row, column=3, padx=5, pady=2)

        # Remove button - fixed width, centered
        remove_btn = ttk.Button(self.scrollable_labels_frame, text="Remove", width=8,
                              command=lambda r=row: self.remove_label(r))
        remove_btn.grid(row=row, column=4, padx=5, pady=2)

        # Store row info
        self.label_rows[row] = {
            'name_entry': name_entry,
            'channel_combo': channel_combo,
            'id_entry': id_entry,
            'simulated_combo': simulated_combo,
            'remove_btn': remove_btn
        }

    def add_label(self):
        """Add a new label row."""
        # Find next available row (start from row 2 after separator, or use max existing + 1)
        max_row = max(self.label_rows.keys()) if self.label_rows else 1
        new_row = max_row + 1

        # Create new row with default values
        self.create_label_row(new_row, "New-Label", 1, "x", "Latitude")

    def remove_label(self, row: int):
        """Remove a label row."""
        if row in self.label_rows:
            # Destroy widgets
            for widget in self.label_rows[row].values():
                widget.destroy()

            # Remove from tracking
            del self.label_rows[row]
            del self.label_name_vars[row]
            del self.label_channel_vars[row]
            del self.label_id_vars[row]
            del self.label_simulated_vars[row]
    
    def save_config(self) -> None:
        """Save configuration and close dialog."""
        try:
            # Save general settings
            self.config_manager.set_value('GENERAL', 'time_acceleration', self.time_accel_var.get())
            self.config_manager.set_value('GENERAL', 'update_interval', self.update_interval_var.get())
            self.config_manager.set_value('GENERAL', 'random_seed', self.random_seed_var.get())
            self.config_manager.set_value('GENERAL', 'movement_margin', self.movement_margin_var.get())
            self.config_manager.set_value('GENERAL', 'pbapro_board_name', self.pbapro_board_var.get())

            # Save channel settings
            for i in range(1, 5):
                if hasattr(self, 'channel_enabled_vars') and i in self.channel_enabled_vars:
                    self.config_manager.set_value('CHANNELS', f'channel_{i}_enabled',
                                                str(self.channel_enabled_vars[i].get()))
                if hasattr(self, 'channel_speed_vars') and i in self.channel_speed_vars:
                    self.config_manager.set_value('CHANNELS', f'channel_{i}_speed',
                                                self.channel_speed_vars[i].get())

            # Save label mappings from Labels tab
            if (hasattr(self, 'label_name_vars') and hasattr(self, 'label_channel_vars') and
                hasattr(self, 'label_id_vars') and hasattr(self, 'label_simulated_vars')):
                # Clear existing labels section
                if self.config_manager.config.has_section('LABELS'):
                    self.config_manager.config.remove_section('LABELS')
                self.config_manager.config.add_section('LABELS')

                # Save current labels
                for row in self.label_rows:
                    if row in self.label_name_vars:
                        label_name = self.label_name_vars[row].get().strip()
                        channel = self.label_channel_vars[row].get()
                        label_id = self.label_id_vars[row].get().strip()
                        simulated_value = self.label_simulated_vars[row].get().strip()

                        # Only save non-empty label names
                        if label_name:
                            self.config_manager.set_value('LABELS', label_name, f"{channel},{label_id},{simulated_value}")

            # Validate configuration
            errors = self.config_manager.validate_config()
            if errors:
                messagebox.showerror("Configuration Error", "\n".join(errors))
                return

            # Save to file
            if self.config_manager.save_config():
                print("Configuration saved successfully")
                self.result = True
                self.dialog.destroy()
            else:
                messagebox.showerror("Error", "Failed to save configuration file")

        except Exception as e:
            messagebox.showerror("Error", f"Error saving configuration: {e}")
    
    def cancel(self) -> None:
        """Cancel and close dialog."""
        self.result = False
        self.dialog.destroy()
