# Diff Details

Date : 2025-06-10 15:11:25

Directory c:\\Users\\<USER>\\Desktop\\A429DataSimulator

Total : 62 files,  3233 codes, 769 comments, 867 blanks, all 4869 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [Attic/A429\_utilities.py](/Attic/A429_utilities.py) | Python | 35 | 24 | 13 | 72 |
| [Attic/airports\_data.py](/Attic/airports_data.py) | Python | 174 | 7 | 5 | 186 |
| [Attic/arinc\_flight\_simulator.py](/Attic/arinc_flight_simulator.py) | Python | 137 | 37 | 40 | 214 |
| [Attic/arinc\_flight\_simulator\_export/A429\_utilities.py](/Attic/arinc_flight_simulator_export/A429_utilities.py) | Python | 33 | 18 | 12 | 63 |
| [Attic/arinc\_flight\_simulator\_export/IMPORT\_IMPROVEMENTS.md](/Attic/arinc_flight_simulator_export/IMPORT_IMPROVEMENTS.md) | Markdown | 117 | 0 | 40 | 157 |
| [Attic/arinc\_flight\_simulator\_export/PATH\_DIAGNOSTICS\_GUIDE.md](/Attic/arinc_flight_simulator_export/PATH_DIAGNOSTICS_GUIDE.md) | Markdown | 160 | 0 | 61 | 221 |
| [Attic/arinc\_flight\_simulator\_export/README.md](/Attic/arinc_flight_simulator_export/README.md) | Markdown | 259 | 0 | 80 | 339 |
| [Attic/arinc\_flight\_simulator\_export/\_\_init\_\_.py](/Attic/arinc_flight_simulator_export/__init__.py) | Python | 17 | 9 | 4 | 30 |
| [Attic/arinc\_flight\_simulator\_export/airports\_data.py](/Attic/arinc_flight_simulator_export/airports_data.py) | Python | 177 | 7 | 5 | 189 |
| [Attic/arinc\_flight\_simulator\_export/arinc\_flight\_simulator.py](/Attic/arinc_flight_simulator_export/arinc_flight_simulator.py) | Python | 311 | 80 | 91 | 482 |
| [Attic/arinc\_flight\_simulator\_export/config\_manager.py](/Attic/arinc_flight_simulator_export/config_manager.py) | Python | 476 | 79 | 90 | 645 |
| [Attic/arinc\_flight\_simulator\_export/diagnose\_paths.py](/Attic/arinc_flight_simulator_export/diagnose_paths.py) | Python | 175 | 30 | 40 | 245 |
| [Attic/arinc\_flight\_simulator\_export/flight\_model.py](/Attic/arinc_flight_simulator_export/flight_model.py) | Python | 90 | 19 | 25 | 134 |
| [Attic/arinc\_flight\_simulator\_export/import\_utils.py](/Attic/arinc_flight_simulator_export/import_utils.py) | Python | 74 | 43 | 25 | 142 |
| [Attic/arinc\_flight\_simulator\_export/requirements.txt](/Attic/arinc_flight_simulator_export/requirements.txt) | pip requirements | 0 | 11 | 3 | 14 |
| [Attic/arinc\_flight\_simulator\_export/run\_simulator.bat](/Attic/arinc_flight_simulator_export/run_simulator.bat) | Batch | 20 | 3 | 4 | 27 |
| [Attic/arinc\_flight\_simulator\_export/run\_simulator.py](/Attic/arinc_flight_simulator_export/run_simulator.py) | Python | 45 | 11 | 11 | 67 |
| [Attic/arinc\_flight\_simulator\_export/setup\_environment.py](/Attic/arinc_flight_simulator_export/setup_environment.py) | Python | 192 | 27 | 43 | 262 |
| [Attic/arinc\_flight\_simulator\_export/simulator\_config.ini](/Attic/arinc_flight_simulator_export/simulator_config.ini) | Ini | 22 | 0 | 4 | 26 |
| [Attic/arinc\_flight\_simulator\_export/start\_simulator.bat](/Attic/arinc_flight_simulator_export/start_simulator.bat) | Batch | 4 | 0 | 1 | 5 |
| [Attic/arinc\_flight\_simulator\_export/test\_continuous\_noise.py](/Attic/arinc_flight_simulator_export/test_continuous_noise.py) | Python | 63 | 15 | 18 | 96 |
| [Attic/arinc\_flight\_simulator\_export/test\_imports.py](/Attic/arinc_flight_simulator_export/test_imports.py) | Python | 132 | 22 | 33 | 187 |
| [Attic/arinc\_flight\_simulator\_export/test\_margin\_effects.py](/Attic/arinc_flight_simulator_export/test_margin_effects.py) | Python | 31 | 9 | 14 | 54 |
| [Attic/arinc\_flight\_simulator\_export/test\_string\_execution.py](/Attic/arinc_flight_simulator_export/test_string_execution.py) | Python | 68 | 18 | 23 | 109 |
| [Attic/arinc\_flight\_simulator\_export/ui.py](/Attic/arinc_flight_simulator_export/ui.py) | Python | 71 | 23 | 25 | 119 |
| [Attic/flight\_model.py](/Attic/flight_model.py) | Python | 78 | 8 | 21 | 107 |
| [Attic/main.py](/Attic/main.py) | Python | 15 | 0 | 2 | 17 |
| [Attic/simulator\_config.ini](/Attic/simulator_config.ini) | Ini | 22 | 0 | 4 | 26 |
| [Attic/ui.py](/Attic/ui.py) | Python | 42 | 2 | 10 | 54 |
| [README.md](/README.md) | Markdown | 194 | 0 | 59 | 253 |
| [arinc\_flight\_simulator\_export/A429\_utilities.py](/arinc_flight_simulator_export/A429_utilities.py) | Python | -33 | -18 | -12 | -63 |
| [arinc\_flight\_simulator\_export/README.md](/arinc_flight_simulator_export/README.md) | Markdown | -198 | 0 | -67 | -265 |
| [arinc\_flight\_simulator\_export/airports\_data.py](/arinc_flight_simulator_export/airports_data.py) | Python | -177 | -7 | -5 | -189 |
| [arinc\_flight\_simulator\_export/arinc\_flight\_simulator.py](/arinc_flight_simulator_export/arinc_flight_simulator.py) | Python | -281 | -75 | -85 | -441 |
| [arinc\_flight\_simulator\_export/config\_manager.py](/arinc_flight_simulator_export/config_manager.py) | Python | -445 | -76 | -87 | -608 |
| [arinc\_flight\_simulator\_export/flight\_model.py](/arinc_flight_simulator_export/flight_model.py) | Python | -78 | -8 | -21 | -107 |
| [arinc\_flight\_simulator\_export/requirements.txt](/arinc_flight_simulator_export/requirements.txt) | pip requirements | 0 | -11 | -3 | -14 |
| [arinc\_flight\_simulator\_export/run\_simulator.bat](/arinc_flight_simulator_export/run_simulator.bat) | Batch | -20 | -3 | -4 | -27 |
| [arinc\_flight\_simulator\_export/run\_simulator.py](/arinc_flight_simulator_export/run_simulator.py) | Python | -23 | -8 | -8 | -39 |
| [arinc\_flight\_simulator\_export/simulator\_config.ini](/arinc_flight_simulator_export/simulator_config.ini) | Ini | -21 | 0 | -4 | -25 |
| [arinc\_flight\_simulator\_export/ui.py](/arinc_flight_simulator_export/ui.py) | Python | -71 | -23 | -25 | -119 |
| [setup.py](/setup.py) | Python | 53 | 7 | 4 | 64 |
| [simulator\_config.ini](/simulator_config.ini) | Ini | 22 | 0 | 4 | 26 |
| [src/arinc\_flight\_simulator/\_\_init\_\_.py](/src/arinc_flight_simulator/__init__.py) | Python | 15 | 16 | 4 | 35 |
| [src/arinc\_flight\_simulator/\_\_main\_\_.py](/src/arinc_flight_simulator/__main__.py) | Python | 3 | 5 | 3 | 11 |
| [src/arinc\_flight\_simulator/arinc/\_\_init\_\_.py](/src/arinc_flight_simulator/arinc/__init__.py) | Python | 4 | 5 | 3 | 12 |
| [src/arinc\_flight\_simulator/arinc/utilities.py](/src/arinc_flight_simulator/arinc/utilities.py) | Python | 34 | 51 | 17 | 102 |
| [src/arinc\_flight\_simulator/config/\_\_init\_\_.py](/src/arinc_flight_simulator/config/__init__.py) | Python | 0 | 5 | 1 | 6 |
| [src/arinc\_flight\_simulator/config/default.ini](/src/arinc_flight_simulator/config/default.ini) | Ini | 22 | 0 | 3 | 25 |
| [src/arinc\_flight\_simulator/core/\_\_init\_\_.py](/src/arinc_flight_simulator/core/__init__.py) | Python | 9 | 5 | 3 | 17 |
| [src/arinc\_flight\_simulator/core/config.py](/src/arinc_flight_simulator/core/config.py) | Python | 219 | 43 | 48 | 310 |
| [src/arinc\_flight\_simulator/core/flight\_model.py](/src/arinc_flight_simulator/core/flight_model.py) | Python | 97 | 72 | 33 | 202 |
| [src/arinc\_flight\_simulator/core/simulator.py](/src/arinc_flight_simulator/core/simulator.py) | Python | 292 | 95 | 91 | 478 |
| [src/arinc\_flight\_simulator/core/simulator\_methods.py](/src/arinc_flight_simulator/core/simulator_methods.py) | Python | 92 | 28 | 31 | 151 |
| [src/arinc\_flight\_simulator/data/\_\_init\_\_.py](/src/arinc_flight_simulator/data/__init__.py) | Python | 6 | 5 | 3 | 14 |
| [src/arinc\_flight\_simulator/data/airports.py](/src/arinc_flight_simulator/data/airports.py) | Python | 172 | 31 | 9 | 212 |
| [src/arinc\_flight\_simulator/main.py](/src/arinc_flight_simulator/main.py) | Python | 57 | 21 | 18 | 96 |
| [src/arinc\_flight\_simulator/ui/\_\_init\_\_.py](/src/arinc_flight_simulator/ui/__init__.py) | Python | 9 | 5 | 3 | 17 |
| [src/arinc\_flight\_simulator/ui/interface.py](/src/arinc_flight_simulator/ui/interface.py) | Python | 76 | 27 | 33 | 136 |
| [tests/\_\_init\_\_.py](/tests/__init__.py) | Python | 0 | 5 | 1 | 6 |
| [tests/test\_arinc\_utilities.py](/tests/test_arinc_utilities.py) | Python | 91 | 40 | 41 | 172 |
| [tests/test\_flight\_model.py](/tests/test_flight_model.py) | Python | 73 | 30 | 29 | 132 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details