#!/usr/bin/env python3
"""
Test script to demonstrate the effect of different movement_margin values.
"""

from flight_model import simulate_flight_data

def test_margin_effects():
    """Test different movement margin values."""
    
    # Test parameters
    start_airport = "EDDF"
    dest_airport = "KJFK"
    random_seed = 123
    test_time = 25  # seconds
    
    margins = [0.0, 0.5, 1.0, 2.0, 5.0]
    
    print("Testing different movement margin values:")
    print("=" * 60)
    
    for margin in margins:
        print(f"\nMovement Margin: {margin}")
        print("-" * 30)
        
        # Get multiple samples at the same time to show consistency
        flight_data = simulate_flight_data(start_airport, dest_airport, test_time, random_seed, margin)
        
        print(f"Altitude: {flight_data['Altitude_of_aircraft_ft']:8.1f} ft")
        print(f"Speed:    {flight_data['Ground_Speed_knots']:8.1f} kts")
        print(f"Heading:  {flight_data['Heading_degrees']:8.1f} °")
        
        # Show noise amplitude by comparing with no-noise version
        if margin > 0:
            no_noise_data = simulate_flight_data(start_airport, dest_airport, test_time, random_seed, 0.0)
            alt_noise = flight_data['Altitude_of_aircraft_ft'] - no_noise_data['Altitude_of_aircraft_ft']
            speed_noise = flight_data['Ground_Speed_knots'] - no_noise_data['Ground_Speed_knots']
            heading_noise = flight_data['Heading_degrees'] - no_noise_data['Heading_degrees']
            
            print(f"Noise:    Alt={alt_noise:+6.1f}ft, Speed={speed_noise:+5.1f}kts, Heading={heading_noise:+5.1f}°")
    
    print("\n" + "=" * 60)
    print("Continuous noise demonstration (margin=1.0):")
    print("Time(s)  Altitude(ft)  Speed(kts)  Heading(°)")
    print("-" * 45)
    
    # Show how values change smoothly over time
    for t in range(0, 30, 2):
        flight_data = simulate_flight_data(start_airport, dest_airport, t, random_seed, 1.0)
        print(f"{t:6d}  {flight_data['Altitude_of_aircraft_ft']:9.1f}  {flight_data['Ground_Speed_knots']:8.1f}  {flight_data['Heading_degrees']:7.1f}")

if __name__ == "__main__":
    test_margin_effects()
