
from ppbase import *
import os


class Arinc429Tester:
    # The used Parameters to simulate
    # Parameters mus be contained in the Parameters from ARINC429 Card!
    AllParams = ["Present-Position---Latitude",
                 "Present-Position---Longitude",
                 "Ground-Speed",
                 "True-Heading",
#                 "TrackAngleTrue",
                 "Baro-Corrected-Altitude1",
                 "Wind-Speed",
                 "Static-Air-Temperature",
                 "Flight-Information",
                 "ACMS-Information-40",
                 "ACMS-Information-41",
                 "ACMS-Information-42",
                 "ACMS-Information-233",
                 "ACMS-Information-234",
                 "ACMS-Information-235",
                 "ACMS-Information-236"
                 ]
    # The Corresponding Parameters of the Flight Simulator
    ParamsFS = ["Latitude_of_aircraft",
               "Longitude_of_aircraft",
               "Ground_Speed",
               "Heading",
               "Altitude_of_aircraft",
               "Indicated_Air_Speed",
               "",
               "",
               "",
               "",
               "",
               "",
               "",
               "",
               ""]
               
    # Used PBA.pro Objects
    A429Resource = PbaProObject("ResourceList.A429-Board2") # PBA.pro Object ID of Simulator Board 
    GlobalConnections = PbaProObject("GlobalConnections") # Global Connection Manager
    DBSPrjFile = PbaProObject("DBSPrjFile") # Parameter-Database Stream Assign
    
    # The Main Directory, where all the Files and the Scripts are located 
    MainDirectory = py_script_dir
    
   
    def __init__(self):
        print(self.AllParams)
        print(self.ParamsFS)
        self.GlobalConnections.Reset()
        self.DBSPrjFile.Init()
        self.A429Resource.Init()
        # Change SbcStreamId
        print(self.A429Resource.DbsStreamId)
        self.A429Resource.DbsStreamId = "ARINC_1"
        print(self.A429Resource.DbsStreamId)
        self.CreateAircraftSelectionDialog()
        
    
    def SetupResource(self,ConfigFile):
        # Init ARINC Board Chanels
        print("Debug A1")
        self.DBSPrjFile.AddFile(os.path.join(self.MainDirectory, "Combined.ppdbs"),0,"") # Database File for Simulator Card 
        self.DBSPrjFile.AddFile(os.path.join(self.MainDirectory, "msflight.ppdbs"),0,"") # Database File for FSUIPC
        print("Debug A2")

        self.DBSPrjFile.LoadAll() # Load added Database Files
        DBS = PbaProObject("Parameters")
        print("dbs: ")
        print(DBS)
        
        self.Labels = []
        
        with open(os.path.join(self.MainDirectory, ConfigFile)) as fh:
            for line in  fh:
                if line.startswith("CHNL"):
                    Items = line.split("=")
                    ChannelNo = int(Items[0][4])
                    Speed = Items[1].strip()
                    Channel = self.A429Resource.GetChannel(ChannelNo)
                    Channel.ChannelType = "Tx"
                    Channel.ChannelSpeed = Speed
                    Channel.DbsChannelId = 1
                else:
                    for Param in self.AllParams:
                        if line.startswith(Param):
                            print("==========================")
                            print("Reanding Param from ini: ")
                            print(Param)
                            Items = line.split("=")[1]
                            Items = Items.split(",")
                            ChannelNo = int(Items[0])
                            print("ChanelNo: " + str(ChannelNo))
                            LabelString = Items[1].strip()
                            LabelNo=0
                            if LabelString == "x":
                                DBSParam = DBS.GetChild("*." + Param)
                                print("DBSParam: ")
                                print(DBSParam)
                                print("DBSParam Parent Children: ")
                                #print(DBSParam.parent().childrenNames())
                                LabelNo = DBSParam.parent().Label # Label No decimal representation (Oct is used in Label)
                            else:
                                LabelNo = int(LabelString,8)
                            print("LabelNo: ") 
                            print(LabelNo)
                            Channel = self.A429Resource.GetChannel(ChannelNo)
                            print("Channel: ")
                            print(Channel)
                            Label = Channel.Send.Setup.LabelList.NewLabel(LabelNo)
                            Label2 = str(Label)
                            print("Label2: ")
                            print(Label2)
                            if Label2 == "Label360 [A429TxLabel]":
                                print("Woopieee")
                                Label.QueueSize = 7
                                Label.Data.SetData(0x16A7D6,0)
                                Label.Data.SetData(0x0CD931,1)
                                Label.Data.SetData(0x164000,2)
                                Label.Data.SetData(0x13A142,3)
                                Label.Data.SetData(0x14E9D9,4)
                                Label.Data.SetData(0x000059,5)
                                Label.Data.SetData(0x208007,6)
                            else:
                                print("Param: ")
                                print(Param)
                                Label.objectName = Param # Name of Label in "ResourceList.A429-Board2.Channel1.Send.Setup.LabelList"
                                print("Label.GetStatusLabel(): ")
                                print(Label.GetStatusLabel())
                                print(Label.childrenNames())
                                print(Label.GetStatusLabel().childrenNames())
                                self.Labels += [Label.GetStatusLabel()]
                            break

        # test
        #import inspect
        #print(inspect.getmembers(Label.__class__.__name__))

        object_methods = [method_name for method_name in dir(Label)
                  if callable(getattr(Label, method_name))]

        for property, value in vars(Label).items():
            print(property, ":", value)

        print(object_methods)

        print("All Labels test:")
        print(self.Labels)
        for Label in self.Labels:
            print(Label.objectName)
            print(Label.childrenNames())
            print(Label.properties())
            print(Label.Data.objectName)
            print(Label.GetChild("Data"))
                            
        self.A429Resource.DBSSync()
        self.A429Resource.AllTxCtrl(True)
        sleep(1000)
        
                        


    def SetupFS(self):
        """
        Setup the Flight Simulator Connection via the global connection manager 
        """
        MSFlight = PbaProObject("ResourceList.MS-Flight1.Parameters")
        
        Lookup = dict()
        for i in range(len(self.AllParams)):
            Lookup[self.AllParams[i]] = self.ParamsFS[i]
        
        for ParamRef in self.GetAllParamRefs():
                    
            # Get Name for FS Param
            FSParam = Lookup.get(ParamRef.objectName,"")
            if len(FSParam):
                # Setup the Connection between FS Param and Param on Resource
                FsParamRef = MSFlight.GetChild(FSParam)
                Connection = self.GlobalConnections.NewPropertyConnection ()
                Connection.Sender.GetChild("Object").SetObjectByPath(FsParamRef.objectPath())
                Connection.Trigger.GetChild("Object").SetObjectByPath(FsParamRef.objectPath())
                Connection.Receiver.GetChild("Object").SetObjectByPath(ParamRef.objectPath())
                Connection.objectName = FSParam + "->" + ParamRef.objectName
        # Start the Connection Manager
        self.GlobalConnections.EstablishConnections = True
        
        
        
        
    def SetupAssign(self):
        Assign = PbaProObject("AssignRoot")
        Assign.Init()
        for  Param in self.GetAllParamRefs():
            Assign.NewAssignEntry (Param.GetParamPtr () )
        
        
    def GetAllParamRefs(self):
        print("GetAllParamRefs()")
        """
        """
        print("GetAllParamRefs() A1")
        print("self.Labels: ")
        print(self.Labels)
        ParamRefs = []
        for Param in self.AllParams:
            print("Param: " + str(Param))
            ParamRef = None
            # ParamRef = PbaProObject("Parameters").GetChild("*." + Param)
            # ParamRefs += [ParamRef]
            for Label in self.Labels:
                ParamRef = Label.GetChild("Parameters." + Param) # original
                # print("///////////////////////////////////////////////////")
                # print(Label.GetChild("Parameters." + Param))
                # print(Label.GetChild("Parameters"))
                # print(Label.GetChild(Param))
                # print(Label.childrenNames())
                
                # print("Printing Children of Label:")
                # print(Label)
                # print(Label.childrenNames("", -1))
                # print("Searching for Child-Parameter: ")
                # print("Parameters." + Param)

                # if(Label.objectName == Param):
                #         print("HOHOHO")
                #         ParamRef = Label
                # print(Label.objectName)
                # print(Label)

                if ParamRef != None:
                    print("Has Child Param Label: " + str(ParamRef))
                    ParamRefs += [ParamRef]
                    break
        print(ParamRefs)
        print("GetAllParamRefs() End")

        return ParamRefs
  

    def GetConfigFiles(self):
        return self.GetFiles("ini")
        
    def GetSimulationFiles(self):
        return self.GetFiles("sim")
        
    def GetFiles(self,Ext):
        Files = []
        for File in os.listdir(self.MainDirectory):
            if File.endswith(Ext):
                Files += [File]
        return Files
        
    def OnRunButton(self,sender):
        print("OnRunButton()...")
        Button = PbaProObject(sender)
        Dialog = Button.parent()
        ConfigFile = Dialog.PlaneSelector.currentText
        Dialog.close()
        print("Setting up Recource from Config File: " + str(ConfigFile))
        self.SetupResource(ConfigFile)
        self.SetupAssign()
        self.SetupStimulationDialog()
    
    def SetupStimulationDialog(self):
        print("SetupSimulationDialog()...")
        PanelManager = PbaProObject("PanelManager")
        Dlg = PanelManager.CreateDialog()
        ParamGroupBox = Dlg.CreateWidgetByPtr("Group Box",Dlg)
        ParamGroupBox.title = "Control Parameter"
        SimulationBox = Dlg.CreateWidgetByPtr("Group Box",Dlg)
        SimulationBox.title = "Simulation"
        MainLayout = Dlg.AddLayout(Dlg,"MainLayout")
        MainLayout.AddWidget(ParamGroupBox,0,0)
        MainLayout.AddWidget(SimulationBox,1,0)
        GroupLayout = Dlg.AddLayout(ParamGroupBox,"GroupLayout")
        SimulLayout = Dlg.AddLayout(SimulationBox,"SimLayout")
        Line = 0 
        
        print(self.GetAllParamRefs())
        
        for Param in self.GetAllParamRefs():
            #print("Param: " + Param)
            Label = Dlg.CreateWidgetByPtr("Label",ParamGroupBox)
            Label.text = Param.objectName
            GroupLayout.AddWidget(Label,Line,0)
            
            # Actual Value
            ParamRes = Dlg.AddResource(Param.objectPath())
            """
            Label = Dlg.CreateWidgetByPtr("Label",ParamGroupBox)
            GroupLayout.AddWidget(Label,Line,1)
            Dlg.CreatePropertyConnection(Param.objectPath(),"ValueStr",Label.objectPath(),"text",Param.objectPath(),"Updated(QObject*)")
            """
            
            Slider = Dlg.CreateWidgetByPtr("Slider",ParamGroupBox)
            GroupLayout.AddWidget(Slider,Line,2)
            Dlg.CreatePropertyConnection(Slider.objectPath(),"value",Param.objectPath(),"ValueNum",Slider.objectPath(),"valueChanged(int)")
            # TODO Get Minimum and Maximum value for the parameter
            if Param.objectName == "Static-Air-Temperature":
                Slider.minimum = -100
                Slider.maximum  = 100
            elif  Param.objectName == "Present-Position---Latitude":
                Slider.minimum = -180
                Slider.maximum  = 179
            elif  Param.objectName == "Present-Position---Longitude":
                Slider.minimum = -180
                Slider.maximum  = 179
            elif  Param.objectName == "Ground-Speed":
                Slider.minimum = 0
                Slider.maximum  = 300
            elif  Param.objectName == "True-Heading":
                Slider.minimum = -180
                Slider.maximum  = 179
            elif  Param.objectName == "Baro-Corrected-Altitude1":
                Slider.minimum = 0
                Slider.maximum  = 36000
          
                    
            
            LineEdit = Dlg.CreateWidgetByPtr("Line Edit",ParamGroupBox)
            GroupLayout.AddWidget(LineEdit,Line,3)
            Dlg.CreatePropertyConnection(Param.objectPath(),"ValueStr",LineEdit.objectPath(),"text",Param.objectPath(),"Updated(QObject*)")
            Dlg.CreatePropertyConnection(LineEdit.objectPath(),"text",Param.objectPath(),"ValueStr",LineEdit.objectPath(),"returnPressed()")
            Line += 1
            
        ComboBox = Dlg.CreateWidgetByPtr("Combo Box",SimulationBox)
        ComboBox.SetItemTitles("|".join(self.GetSimulationFiles()))
        SimulLayout.AddWidget(ComboBox,0,0)
        self.SimulationComboBox = ComboBox
        
        PushButton = Dlg.CreateWidgetByPtr("Push Button",SimulationBox)
        PushButton.text = "Run"
        SimulLayout.AddWidget(PushButton,0,1)
        connect(PushButton,"clicked()",self,"StartSimulation")
        
        PushButton = Dlg.CreateWidgetByPtr("Push Button",SimulationBox)
        PushButton.text = "Stop"
        SimulLayout.AddWidget(PushButton,0,2)
        connect(PushButton,"clicked()",self,"StopSimulation")
        
        self.ContainerForTimer = Dlg.CreateWidgetByPtr("Widget",SimulationBox)
        SimulLayout.AddWidget(self.ContainerForTimer,0,3)
        
        Dlg.Run()
        Dlg.Run()
        Dlg.Run()
        Dlg.Run()
        Dlg.Run()
        Dlg.Run()
        Dlg.Run()
        Dlg.Run()
        Dlg.Run()
        Dlg.Run()
        Dlg.Run()
        Dlg.Run()
        Dlg.Run()
        Dlg.Run()
        Dlg.Run()
        Dlg.Run()
        Dlg.Run()
        Dlg.Run()

        
    def StartSimulation(self,sender):
        SimFile = self.SimulationComboBox.currentText
        self.StopSimulation(None)
        #SFile = file(self.MainDirectory + "/" +  SimFile)
        bRunAssign = False
        with open(os.path.join(self.MainDirectory, SimFile)) as fh:
            for line in  fh:
                line = line.strip()
                if line.startswith("FS"):
                    print("Setup for MS  Flight")
                    self.SetupFS()
                    break
                else:
                    for Param in self.AllParams:
                        if line.startswith(Param):
                            Func = line.split("=")[1].strip()
                            IsFixed = False
                            try:
                                float(Func)
                                IsFixed = True
                            except:
                                pass
                            if IsFixed:
                                self.SetFixedValue(Param,float(Func))
                            else:
                                self.SetSimulationValue(Param,Func)
                                bRunAssign = True
                                
        if bRunAssign:
            Assign = PbaProObject("AssignRoot")
            Assign.Run(True)
        
    def StopSimulation(self,sender):
        print("SimulationStopped")
        self.GlobalConnections.Reset()
        self.SetupAssign()
        
    def SetFixedValue(self,ParamName,FixedValue):
        for Param in self.GetAllParamRefs():
            if Param.objectName == ParamName:
                Param.ValueStr = FixedValue
                return
        printError(ParamName + " not found")
        
        
    def SetSimulationValue(self,ParamName,FunctionStr):
        Assign = PbaProObject("AssignRoot")
        AssEntry = Assign.GetChild(ParamName)
        if AssEntry != None:
            Func = AssEntry.CreateAssignOption ("Function")
            Func.Rate = 1000
            Func.Enabled = True
            Func.Function = "x:=t;" + FunctionStr
            
      
        
    def CreateAircraftSelectionDialog(self):
        PanelManager = PbaProObject("PanelManager")
        Dialog = PanelManager.LoadTemplateByPath(self.MainDirectory + "/ArincMain.tmpl")
        Dialog.PlaneSelector.SetItemTitles("|".join(self.GetConfigFiles()))
        connect(Dialog.RunButton,"clicked()",self,"OnRunButton")
        
      
    
GlobalTesterInstance = Arinc429Tester()