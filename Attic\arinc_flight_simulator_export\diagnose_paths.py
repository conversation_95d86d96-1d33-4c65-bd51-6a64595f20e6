#!/usr/bin/env python3
"""
Python Path Diagnostic Tool for ARINC Flight Simulator

This script provides detailed information about Python's package search paths
and helps diagnose import issues by showing exactly where Python is looking
for modules and packages.

Usage:
    python diagnose_paths.py
"""

import sys
import os
from pathlib import Path
import importlib.util

def get_current_dir():
    """Get current directory, handling cases where __file__ is not defined."""
    try:
        return Path(__file__).parent
    except NameError:
        return Path.cwd()

def analyze_python_environment():
    """Analyze and display Python environment information."""
    print("=" * 70)
    print("PYTHON ENVIRONMENT ANALYSIS")
    print("=" * 70)
    
    print(f"Python executable: {sys.executable}")
    print(f"Python version: {sys.version}")
    print(f"Python version info: {sys.version_info}")
    print(f"Platform: {sys.platform}")
    print(f"Architecture: {sys.maxsize > 2**32 and '64-bit' or '32-bit'}")
    
    # Current directories
    print(f"\nDIRECTORY INFORMATION:")
    print(f"Current working directory: {Path.cwd()}")
    
    try:
        script_dir = Path(__file__).parent.absolute()
        print(f"Script directory: {script_dir}")
        print(f"Script file: {Path(__file__).absolute()}")
    except NameError:
        print("Script directory: __file__ not available (string execution context)")
    
    # Python installation paths
    print(f"\nPYTHON INSTALLATION PATHS:")
    python_home = Path(sys.executable).parent
    print(f"Python home: {python_home}")
    
    lib_paths = [
        python_home / "Lib",
        python_home / "Lib" / "site-packages",
        python_home / "Scripts",
    ]
    
    for lib_path in lib_paths:
        exists = "✓" if lib_path.exists() else "✗"
        print(f"  {exists} {lib_path}")

def analyze_sys_path():
    """Analyze sys.path in detail."""
    print(f"\nSYS.PATH ANALYSIS ({len(sys.path)} entries):")
    print("-" * 70)
    
    for i, path_str in enumerate(sys.path):
        path_obj = Path(path_str)
        
        # Check if path exists and what type it is
        if path_obj.exists():
            if path_obj.is_dir():
                path_type = "DIR"
                status = "✓"
            elif path_obj.is_file():
                path_type = "FILE"
                status = "✓"
            else:
                path_type = "OTHER"
                status = "?"
        else:
            path_type = "MISSING"
            status = "✗"
        
        # Check if it's relative or absolute
        is_absolute = path_obj.is_absolute()
        abs_indicator = "ABS" if is_absolute else "REL"
        
        print(f"{i:2d}: {status} [{path_type:7s}] [{abs_indicator}] {path_str}")
        
        # For directories, show some contents
        if path_obj.exists() and path_obj.is_dir():
            try:
                # Count Python files
                py_files = list(path_obj.glob("*.py"))
                if py_files:
                    print(f"    └─ Contains {len(py_files)} .py files")
                
                # Check for common packages
                common_packages = ['tkinter', 'pathlib', 'configparser']
                found_packages = []
                for pkg in common_packages:
                    if (path_obj / f"{pkg}.py").exists() or (path_obj / pkg).exists():
                        found_packages.append(pkg)
                
                if found_packages:
                    print(f"    └─ Standard packages: {', '.join(found_packages)}")
                    
            except (PermissionError, OSError):
                print(f"    └─ Cannot access directory contents")

def search_for_simulator_files():
    """Search for ARINC Flight Simulator files in sys.path."""
    print(f"\nSIMULATOR FILE SEARCH:")
    print("-" * 70)
    
    simulator_files = [
        'ui.py',
        'flight_model.py', 
        'arinc_flight_simulator.py',
        'A429_utilities.py',
        'config_manager.py',
        'airports_data.py',
        'import_utils.py'
    ]
    
    found_locations = {}
    
    for path_str in sys.path:
        path_obj = Path(path_str)
        if path_obj.exists() and path_obj.is_dir():
            found_files = []
            for sim_file in simulator_files:
                file_path = path_obj / sim_file
                if file_path.exists():
                    found_files.append(sim_file)
                    if sim_file not in found_locations:
                        found_locations[sim_file] = []
                    found_locations[sim_file].append(str(path_obj))
            
            if found_files:
                print(f"✓ {path_obj}")
                for found_file in found_files:
                    print(f"  └─ {found_file}")
    
    # Summary of file locations
    print(f"\nFILE LOCATION SUMMARY:")
    for sim_file in simulator_files:
        if sim_file in found_locations:
            locations = found_locations[sim_file]
            print(f"✓ {sim_file}: found in {len(locations)} location(s)")
            for loc in locations:
                print(f"  └─ {loc}")
        else:
            print(f"✗ {sim_file}: not found in any sys.path location")

def test_import_mechanisms():
    """Test different import mechanisms."""
    print(f"\nIMPORT MECHANISM TESTS:")
    print("-" * 70)
    
    # Test 1: Direct import
    print("1. Testing direct imports:")
    test_modules = ['ui', 'flight_model', 'arinc_flight_simulator']
    
    for module_name in test_modules:
        try:
            spec = importlib.util.find_spec(module_name)
            if spec:
                print(f"   ✓ {module_name}: found at {spec.origin}")
            else:
                print(f"   ✗ {module_name}: not found")
        except Exception as e:
            print(f"   ✗ {module_name}: error - {e}")
    
    # Test 2: Current directory import
    print("\n2. Testing current directory:")
    current_dir = get_current_dir()
    print(f"   Current directory: {current_dir}")
    
    for module_name in test_modules:
        module_file = current_dir / f"{module_name}.py"
        exists = "✓" if module_file.exists() else "✗"
        print(f"   {exists} {module_name}.py")

def provide_recommendations():
    """Provide recommendations based on the analysis."""
    print(f"\nRECOMMENDATIONS:")
    print("-" * 70)
    
    current_dir = get_current_dir()
    simulator_files = ['ui.py', 'flight_model.py', 'arinc_flight_simulator.py']
    
    # Check if simulator files are in current directory
    files_in_current = [f for f in simulator_files if (current_dir / f).exists()]
    
    if len(files_in_current) == len(simulator_files):
        print("✓ All simulator files found in current directory")
        print("  Recommendation: Run scripts from this directory")
        
        if str(current_dir) not in sys.path:
            print("⚠ Current directory not in sys.path")
            print("  Solution: Add current directory to sys.path")
            print(f"  Code: sys.path.insert(0, '{current_dir}')")
    else:
        print("✗ Simulator files not found in current directory")
        print("  Recommendations:")
        print("  1. Navigate to the directory containing the simulator files")
        print("  2. Use the find_and_run.py script to automatically locate files")
        print("  3. Add the simulator directory to PYTHONPATH environment variable")
    
    # Check working directory vs script directory
    try:
        script_dir = Path(__file__).parent.absolute()
        if script_dir != Path.cwd():
            print(f"\n⚠ Working directory differs from script directory")
            print(f"  Working dir: {Path.cwd()}")
            print(f"  Script dir:  {script_dir}")
            print(f"  Solution: cd \"{script_dir}\"")
    except NameError:
        pass

def main():
    """Main diagnostic function."""
    print("ARINC Flight Simulator - Python Path Diagnostics")
    
    analyze_python_environment()
    analyze_sys_path()
    search_for_simulator_files()
    test_import_mechanisms()
    provide_recommendations()
    
    print("\n" + "=" * 70)
    print("DIAGNOSTIC COMPLETE")
    print("=" * 70)
    print("\nIf you're still having import issues:")
    print("1. Use the recommendations above")
    print("2. Run: python setup_environment.py")
    print("3. Run: python test_imports.py")
    print("4. Use: python find_and_run.py")

if __name__ == "__main__":
    main()
