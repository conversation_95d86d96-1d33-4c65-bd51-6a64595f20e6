# ARINC Flight Simulator Dependencies
# This file lists the Python packages required to run the ARINC Flight Simulator

# Standard library modules used (no installation required):
# - tkinter (GUI framework - included with Python)
# - threading (multithreading support - included with Python)
# - time (time-related functions - included with Python)
# - os (operating system interface - included with Python)
# - math (mathematical functions - included with Python)
# - random (random number generation - included with Python)

# No external dependencies required!
# The simulator uses only Python standard library modules.
