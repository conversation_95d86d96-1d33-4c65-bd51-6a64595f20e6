import configparser
import os
import tkinter as tk
from tkinter import ttk, messagebox

class ConfigManager:
    def __init__(self, config_file="simulator_config.ini"):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        
        # Default configuration values
        self.defaults = {
            'GENERAL': {
                'time_acceleration': '10',
                'update_interval': '1.0',
                'pbapro_board_name': 'ResourceList.A429-Board2',
                'random_seed': '123',
                'movement_margin': '1.0'
            },
            'CHANNELS': {
                'channel_1_speed': '12.5',
                'channel_2_speed': '12.5', 
                'channel_3_speed': 'Low',
                'channel_4_speed': 'Low',
                'channel_1_enabled': 'True',
                'channel_2_enabled': 'True',
                'channel_3_enabled': 'False',
                'channel_4_enabled': 'False'
            },
            'LABELS': {
                'Present-Position---Latitude': '1,x',
                'Present-Position---Longitude': '1,x',
                'Ground-Speed': '1,x',
                'True-Heading': '1,x',
                'Baro-Corrected-Altitude1': '2,x',
                'Static-Air-Temperature': '2,x'
            }
        }
        
        self.load_config()
    
    def load_config(self):
        """Load configuration from file or create with defaults."""
        if os.path.exists(self.config_file):
            try:
                self.config.read(self.config_file)
                print(f"Configuration loaded from {self.config_file}")
            except Exception as e:
                print(f"Error loading config file: {e}")
                self.create_default_config()
        else:
            print(f"Config file {self.config_file} not found, creating with defaults")
            self.create_default_config()
    
    def create_default_config(self):
        """Create configuration file with default values."""
        for section, options in self.defaults.items():
            self.config.add_section(section)
            for key, value in options.items():
                self.config.set(section, key, value)
        self.save_config()
    
    def save_config(self):
        """Save current configuration to file."""
        try:
            with open(self.config_file, 'w') as f:
                self.config.write(f)
            print(f"Configuration saved to {self.config_file}")
            return True
        except Exception as e:
            print(f"Error saving config file: {e}")
            return False
    
    def get_value(self, section, key, fallback=None):
        """Get configuration value with fallback."""
        try:
            value = self.config.get(section, key)
            if value is not None and value.strip():
                return value
        except:
            pass

        # Use fallback or default
        if fallback is not None:
            return fallback
        return self.defaults.get(section, {}).get(key, '')
    
    def get_int(self, section, key, fallback=None):
        """Get integer configuration value."""
        try:
            value = self.config.getint(section, key)
            return value
        except:
            if fallback is not None:
                return fallback
            default_str = self.defaults.get(section, {}).get(key, '0')
            try:
                return int(default_str)
            except:
                return 0

    def get_float(self, section, key, fallback=None):
        """Get float configuration value."""
        try:
            value = self.config.getfloat(section, key)
            return value
        except:
            if fallback is not None:
                return fallback
            default_str = self.defaults.get(section, {}).get(key, '0.0')
            try:
                return float(default_str)
            except:
                return 0.0

    def get_boolean(self, section, key, fallback=None):
        """Get boolean configuration value."""
        try:
            value = self.config.getboolean(section, key)
            return value
        except:
            if fallback is not None:
                return fallback
            default_str = self.defaults.get(section, {}).get(key, 'False')
            return default_str.lower() == 'true'
    
    def set_value(self, section, key, value):
        """Set configuration value."""
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, key, str(value))
    
    def get_channel_config(self):
        """Get channel configuration as dictionary."""
        channels = {}
        for i in range(1, 5):
            if self.get_boolean('CHANNELS', f'channel_{i}_enabled'):
                speed = self.get_value('CHANNELS', f'channel_{i}_speed')
                channels[i] = speed
        return channels
    
    def get_label_mapping(self):
        """Get label to channel mapping."""
        mapping = {}
        if self.config.has_section('LABELS'):
            for param, value in self.config.items('LABELS'):
                try:
                    channel, label = value.split(',')
                    mapping[param] = (int(channel), label.strip())
                except:
                    print(f"Invalid label configuration for {param}: {value}")
        return mapping
    
    def validate_config(self):
        """Validate configuration values."""
        errors = []
        
        # Validate time acceleration
        try:
            acc = self.get_int('GENERAL', 'time_acceleration')
            if acc < 1 or acc > 100:
                errors.append("Time acceleration must be between 1 and 100")
        except:
            errors.append("Time acceleration must be a valid integer")
        
        # Validate update interval
        try:
            interval = self.get_float('GENERAL', 'update_interval')
            if interval < 0.1 or interval > 10.0:
                errors.append("Update interval must be between 0.1 and 10.0 seconds")
        except:
            errors.append("Update interval must be a valid number")

        # Validate random seed
        try:
            seed = self.get_int('GENERAL', 'random_seed')
            if seed < 1 or seed > 999999:
                errors.append("Random seed must be between 1 and 999999")
        except:
            errors.append("Random seed must be a valid integer")

        # Validate movement margin
        try:
            margin = self.get_float('GENERAL', 'movement_margin')
            if margin < 0.0 or margin > 10.0:
                errors.append("Movement margin must be between 0.0 and 10.0")
        except:
            errors.append("Movement margin must be a valid number")
        
        # Validate channel speeds
        valid_speeds = ['12.5', '100', 'Low', 'High']
        for i in range(1, 5):
            speed = self.get_value('CHANNELS', f'channel_{i}_speed')
            if speed not in valid_speeds:
                errors.append(f"Channel {i} speed must be one of: {', '.join(valid_speeds)}")
        
        # Validate label mappings
        for param, value in self.config.items('LABELS'):
            try:
                channel, label = value.split(',')
                channel_num = int(channel)
                if channel_num < 1 or channel_num > 4:
                    errors.append(f"Label {param}: Channel must be 1-4")
                if label.strip() not in ['x'] and not label.strip().isdigit():
                    errors.append(f"Label {param}: Label must be 'x' or octal number")
            except:
                errors.append(f"Label {param}: Invalid format (should be 'channel,label')")
        
        return errors

class ConfigDialog:
    def __init__(self, parent, config_manager):
        self.parent = parent
        self.config_manager = config_manager
        self.result = None
        self.dialog = None

    def show_dialog(self):
        """Show configuration dialog."""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("ARINC Simulator Configuration")
        self.dialog.geometry("600x500")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")

        self.create_widgets()
        self.load_current_values()

        # Wait for dialog to close
        self.dialog.wait_window()
        return self.result

    def create_widgets(self):
        """Create dialog widgets."""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.dialog)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # General settings tab
        general_frame = ttk.Frame(notebook)
        notebook.add(general_frame, text="General")
        self.create_general_tab(general_frame)

        # Channel settings tab
        channel_frame = ttk.Frame(notebook)
        notebook.add(channel_frame, text="Channels")
        self.create_channel_tab(channel_frame)

        # Label settings tab
        label_frame = ttk.Frame(notebook)
        notebook.add(label_frame, text="Labels")
        self.create_label_tab(label_frame)

        # Buttons
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(button_frame, text="OK", command=self.ok_clicked).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=self.cancel_clicked).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Reset to Defaults", command=self.reset_clicked).pack(side=tk.LEFT, padx=5)

    def create_general_tab(self, parent):
        """Create general settings tab."""
        # Time acceleration
        ttk.Label(parent, text="Time Acceleration (1-100):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.time_accel_var = tk.StringVar()
        time_accel_spin = ttk.Spinbox(parent, from_=1, to=100, textvariable=self.time_accel_var, width=10)
        time_accel_spin.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.time_accel_error = ttk.Label(parent, text="", foreground="red")
        self.time_accel_error.grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)

        # Update interval
        ttk.Label(parent, text="Update Interval (0.1-10.0 sec):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.update_interval_var = tk.StringVar()
        update_entry = ttk.Entry(parent, textvariable=self.update_interval_var, width=10)
        update_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        self.update_interval_error = ttk.Label(parent, text="", foreground="red")
        self.update_interval_error.grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)

        # PbaPro board name
        ttk.Label(parent, text="PbaPro Board Name:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.board_name_var = tk.StringVar()
        board_entry = ttk.Entry(parent, textvariable=self.board_name_var, width=30)
        board_entry.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        self.board_name_error = ttk.Label(parent, text="", foreground="red")
        self.board_name_error.grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)

        # Random seed
        ttk.Label(parent, text="Random Seed (1-999999):").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.random_seed_var = tk.StringVar()
        seed_spin = ttk.Spinbox(parent, from_=1, to=999999, textvariable=self.random_seed_var, width=10)
        seed_spin.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        self.random_seed_error = ttk.Label(parent, text="", foreground="red")
        self.random_seed_error.grid(row=3, column=2, sticky=tk.W, padx=5, pady=5)

        # Movement margin
        ttk.Label(parent, text="Movement Margin (0.0-10.0):").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.movement_margin_var = tk.StringVar()
        margin_entry = ttk.Entry(parent, textvariable=self.movement_margin_var, width=10)
        margin_entry.grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        self.movement_margin_error = ttk.Label(parent, text="", foreground="red")
        self.movement_margin_error.grid(row=4, column=2, sticky=tk.W, padx=5, pady=5)

        # Bind validation events
        self.time_accel_var.trace_add('write', self.validate_time_accel)
        self.update_interval_var.trace_add('write', self.validate_update_interval)
        self.board_name_var.trace_add('write', self.validate_board_name)
        self.random_seed_var.trace_add('write', self.validate_random_seed)
        self.movement_margin_var.trace_add('write', self.validate_movement_margin)

    def create_channel_tab(self, parent):
        """Create channel settings tab."""
        # Headers
        ttk.Label(parent, text="Channel").grid(row=0, column=0, padx=5, pady=5)
        ttk.Label(parent, text="Enabled").grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(parent, text="Speed").grid(row=0, column=2, padx=5, pady=5)
        ttk.Label(parent, text="Status").grid(row=0, column=3, padx=5, pady=5)

        self.channel_enabled_vars = {}
        self.channel_speed_vars = {}
        self.channel_error_labels = {}

        speed_options = ['12.5', '100', 'Low', 'High']

        for i in range(1, 5):
            # Channel number
            ttk.Label(parent, text=f"Channel {i}").grid(row=i, column=0, padx=5, pady=5)

            # Enabled checkbox
            enabled_var = tk.BooleanVar()
            self.channel_enabled_vars[i] = enabled_var
            ttk.Checkbutton(parent, variable=enabled_var).grid(row=i, column=1, padx=5, pady=5)

            # Speed dropdown
            speed_var = tk.StringVar()
            self.channel_speed_vars[i] = speed_var
            speed_combo = ttk.Combobox(parent, textvariable=speed_var, values=speed_options, state="readonly", width=10)
            speed_combo.grid(row=i, column=2, padx=5, pady=5)

            # Error label
            error_label = ttk.Label(parent, text="", foreground="red")
            error_label.grid(row=i, column=3, padx=5, pady=5)
            self.channel_error_labels[i] = error_label

            # Bind validation events
            speed_var.trace_add('write', lambda *args, ch=i: self.validate_channel_speed(ch))
            enabled_var.trace_add('write', lambda *args, ch=i: self.validate_channel_config(ch))

    def create_label_tab(self, parent):
        """Create label settings tab."""
        # Headers
        ttk.Label(parent, text="Parameter").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        ttk.Label(parent, text="Channel").grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(parent, text="Label").grid(row=0, column=2, padx=5, pady=5)
        ttk.Label(parent, text="Status").grid(row=0, column=3, padx=5, pady=5)

        self.label_channel_vars = {}
        self.label_label_vars = {}
        self.label_error_labels = {}

        params = [
            "Present-Position---Latitude",
            "Present-Position---Longitude",
            "Ground-Speed",
            "True-Heading",
            "Baro-Corrected-Altitude1",
            "Static-Air-Temperature"
        ]

        for i, param in enumerate(params, 1):
            # Parameter name
            ttk.Label(parent, text=param).grid(row=i, column=0, padx=5, pady=5, sticky=tk.W)

            # Channel dropdown
            channel_var = tk.StringVar()
            self.label_channel_vars[param] = channel_var
            channel_combo = ttk.Combobox(parent, textvariable=channel_var, values=['1', '2', '3', '4'],
                                       state="readonly", width=5)
            channel_combo.grid(row=i, column=1, padx=5, pady=5)

            # Label entry
            label_var = tk.StringVar()
            self.label_label_vars[param] = label_var
            label_entry = ttk.Entry(parent, textvariable=label_var, width=10)
            label_entry.grid(row=i, column=2, padx=5, pady=5)

            # Error label
            error_label = ttk.Label(parent, text="", foreground="red")
            error_label.grid(row=i, column=3, padx=5, pady=5)
            self.label_error_labels[param] = error_label

            # Bind validation events
            channel_var.trace_add('write', lambda *args, p=param: self.validate_label_mapping(p))
            label_var.trace_add('write', lambda *args, p=param: self.validate_label_mapping(p))

    def load_current_values(self):
        """Load current configuration values into dialog."""
        # General settings - always use defaults if values are missing/invalid
        self.time_accel_var.set(str(self.config_manager.get_int('GENERAL', 'time_acceleration')))
        self.update_interval_var.set(str(self.config_manager.get_float('GENERAL', 'update_interval')))
        self.board_name_var.set(self.config_manager.get_value('GENERAL', 'pbapro_board_name'))
        self.random_seed_var.set(str(self.config_manager.get_int('GENERAL', 'random_seed')))
        self.movement_margin_var.set(str(self.config_manager.get_float('GENERAL', 'movement_margin')))

        # Channel settings - use defaults for missing values
        for i in range(1, 5):
            enabled = self.config_manager.get_boolean('CHANNELS', f'channel_{i}_enabled')
            speed = self.config_manager.get_value('CHANNELS', f'channel_{i}_speed')
            self.channel_enabled_vars[i].set(enabled)
            self.channel_speed_vars[i].set(speed)

        # Label settings - use defaults for missing mappings
        label_mapping = self.config_manager.get_label_mapping()
        for param in self.label_channel_vars.keys():
            if param in label_mapping:
                channel, label = label_mapping[param]
                self.label_channel_vars[param].set(str(channel))
                self.label_label_vars[param].set(label)
            else:
                # Use default values from config manager defaults
                default_mapping = self.config_manager.defaults['LABELS'].get(param, '1,x')
                try:
                    channel, label = default_mapping.split(',')
                    self.label_channel_vars[param].set(channel.strip())
                    self.label_label_vars[param].set(label.strip())
                except:
                    # Fallback if default is malformed
                    self.label_channel_vars[param].set('1')
                    self.label_label_vars[param].set('x')

        # Trigger initial validation
        self.validate_all_fields()

    def validate_and_save(self):
        """Validate input and save configuration."""
        # Run validation on all fields first
        self.validate_all_fields()

        # Check if any validation errors exist by looking at error labels
        errors = []

        # Check general settings errors
        if "✗" in self.time_accel_error.cget("text"):
            errors.append("Time acceleration: " + self.time_accel_error.cget("text").replace("✗ ", ""))
        if "✗" in self.update_interval_error.cget("text"):
            errors.append("Update interval: " + self.update_interval_error.cget("text").replace("✗ ", ""))
        if "✗" in self.board_name_error.cget("text"):
            errors.append("Board name: " + self.board_name_error.cget("text").replace("✗ ", ""))
        if "✗" in self.random_seed_error.cget("text"):
            errors.append("Random seed: " + self.random_seed_error.cget("text").replace("✗ ", ""))
        if "✗" in self.movement_margin_error.cget("text"):
            errors.append("Movement margin: " + self.movement_margin_error.cget("text").replace("✗ ", ""))

        # Check channel errors
        for i in range(1, 5):
            error_text = self.channel_error_labels[i].cget("text")
            if "✗" in error_text:
                errors.append(f"Channel {i}: {error_text.replace('✗ ', '')}")

        # Check label errors
        for param in self.label_error_labels.keys():
            error_text = self.label_error_labels[param].cget("text")
            if "✗" in error_text:
                errors.append(f"{param}: {error_text.replace('✗ ', '')}")

        if errors:
            messagebox.showerror("Configuration Errors",
                               "Please fix the following errors:\n\n" + "\n".join(errors))
            return False

        # Save configuration
        self.config_manager.set_value('GENERAL', 'time_acceleration', self.time_accel_var.get())
        self.config_manager.set_value('GENERAL', 'update_interval', self.update_interval_var.get())
        self.config_manager.set_value('GENERAL', 'pbapro_board_name', self.board_name_var.get())
        self.config_manager.set_value('GENERAL', 'random_seed', self.random_seed_var.get())
        self.config_manager.set_value('GENERAL', 'movement_margin', self.movement_margin_var.get())

        for i in range(1, 5):
            self.config_manager.set_value('CHANNELS', f'channel_{i}_enabled', self.channel_enabled_vars[i].get())
            self.config_manager.set_value('CHANNELS', f'channel_{i}_speed', self.channel_speed_vars[i].get())

        for param in self.label_channel_vars.keys():
            channel = self.label_channel_vars[param].get()
            label = self.label_label_vars[param].get()
            self.config_manager.set_value('LABELS', param, f"{channel},{label}")

        return self.config_manager.save_config()

    def ok_clicked(self):
        """Handle OK button click."""
        if self.validate_and_save():
            self.result = True
            self.dialog.destroy()

    def cancel_clicked(self):
        """Handle Cancel button click."""
        self.result = False
        self.dialog.destroy()

    def reset_clicked(self):
        """Handle Reset to Defaults button click."""
        if messagebox.askyesno("Reset Configuration", "Reset all settings to default values?"):
            self.config_manager.create_default_config()
            self.load_current_values()

    # Real-time validation methods
    def validate_time_accel(self, *args):
        """Validate time acceleration field."""
        try:
            value = int(self.time_accel_var.get())
            if 1 <= value <= 100:
                self.time_accel_error.config(text="✓", foreground="green")
            else:
                self.time_accel_error.config(text="✗ Must be 1-100", foreground="red")
        except ValueError:
            if self.time_accel_var.get().strip():
                self.time_accel_error.config(text="✗ Invalid number", foreground="red")
            else:
                self.time_accel_error.config(text="✗ Required", foreground="red")

    def validate_update_interval(self, *args):
        """Validate update interval field."""
        try:
            value = float(self.update_interval_var.get())
            if 0.1 <= value <= 10.0:
                self.update_interval_error.config(text="✓", foreground="green")
            else:
                self.update_interval_error.config(text="✗ Must be 0.1-10.0", foreground="red")
        except ValueError:
            if self.update_interval_var.get().strip():
                self.update_interval_error.config(text="✗ Invalid number", foreground="red")
            else:
                self.update_interval_error.config(text="✗ Required", foreground="red")

    def validate_board_name(self, *args):
        """Validate board name field."""
        value = self.board_name_var.get().strip()
        if value:
            self.board_name_error.config(text="✓", foreground="green")
        else:
            self.board_name_error.config(text="✗ Required", foreground="red")

    def validate_random_seed(self, *args):
        """Validate random seed field."""
        try:
            value = int(self.random_seed_var.get())
            if 1 <= value <= 999999:
                self.random_seed_error.config(text="✓", foreground="green")
            else:
                self.random_seed_error.config(text="✗ Must be 1-999999", foreground="red")
        except ValueError:
            if self.random_seed_var.get().strip():
                self.random_seed_error.config(text="✗ Invalid number", foreground="red")
            else:
                self.random_seed_error.config(text="✗ Required", foreground="red")

    def validate_movement_margin(self, *args):
        """Validate movement margin field."""
        try:
            value = float(self.movement_margin_var.get())
            if 0.0 <= value <= 10.0:
                self.movement_margin_error.config(text="✓", foreground="green")
            else:
                self.movement_margin_error.config(text="✗ Must be 0.0-10.0", foreground="red")
        except ValueError:
            if self.movement_margin_var.get().strip():
                self.movement_margin_error.config(text="✗ Invalid number", foreground="red")
            else:
                self.movement_margin_error.config(text="✗ Required", foreground="red")

    def validate_channel_speed(self, channel):
        """Validate channel speed setting."""
        speed = self.channel_speed_vars[channel].get()
        valid_speeds = ['12.5', '100', 'Low', 'High']

        if speed in valid_speeds:
            self.channel_error_labels[channel].config(text="✓", foreground="green")
        elif speed:
            self.channel_error_labels[channel].config(text="✗ Invalid speed", foreground="red")
        else:
            self.channel_error_labels[channel].config(text="✗ Select speed", foreground="red")

    def validate_channel_config(self, channel):
        """Validate overall channel configuration."""
        enabled = self.channel_enabled_vars[channel].get()
        speed = self.channel_speed_vars[channel].get()

        if enabled and not speed:
            self.channel_error_labels[channel].config(text="✗ Speed required", foreground="red")
        elif enabled and speed:
            self.validate_channel_speed(channel)
        else:
            self.channel_error_labels[channel].config(text="", foreground="black")

    def validate_label_mapping(self, param):
        """Validate label mapping for a parameter."""
        channel = self.label_channel_vars[param].get()
        label = self.label_label_vars[param].get()

        errors = []

        if not channel:
            errors.append("Channel required")
        else:
            try:
                ch_num = int(channel)
                if ch_num < 1 or ch_num > 4:
                    errors.append("Channel 1-4")
            except ValueError:
                errors.append("Invalid channel")

        if not label:
            errors.append("Label required")
        elif label not in ['x'] and not (label.isdigit() and len(label) <= 3):
            errors.append("Use 'x' or octal")

        if errors:
            self.label_error_labels[param].config(text="✗ " + ", ".join(errors), foreground="red")
        else:
            self.label_error_labels[param].config(text="✓", foreground="green")

    def validate_all_fields(self):
        """Validate all fields on initial load."""
        # Validate general settings
        self.validate_time_accel()
        self.validate_update_interval()
        self.validate_board_name()
        self.validate_random_seed()
        self.validate_movement_margin()

        # Validate channels
        for i in range(1, 5):
            self.validate_channel_config(i)

        # Validate labels
        for param in self.label_channel_vars.keys():
            self.validate_label_mapping(param)
