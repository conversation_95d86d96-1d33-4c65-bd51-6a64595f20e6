"""
Import utilities for ARINC Flight Simulator

This module provides utilities to handle local imports more robustly
across different Python environments and execution contexts.
"""

import os
import sys
from pathlib import Path


def setup_local_imports():
    """
    Setup the Python path to ensure local modules can be imported.

    This function adds the current directory and parent directories
    to sys.path to handle various execution contexts.
    """
    # Get the directory containing this file
    try:
        current_file_dir = Path(__file__).parent.absolute()
    except NameError:
        # Fallback to current working directory if __file__ is not defined
        current_file_dir = Path.cwd().absolute()
    
    # Add current directory to path if not already there
    current_dir_str = str(current_file_dir)
    if current_dir_str not in sys.path:
        sys.path.insert(0, current_dir_str)
    
    # Also add parent directory in case we're being imported from outside
    parent_dir = current_file_dir.parent
    parent_dir_str = str(parent_dir)
    if parent_dir_str not in sys.path:
        sys.path.insert(0, parent_dir_str)


def safe_import(module_name, package=None):
    """
    Safely import a module with better error reporting.
    
    Args:
        module_name (str): Name of the module to import
        package (str, optional): Package name for relative imports
        
    Returns:
        module: The imported module, or None if import failed
    """
    try:
        if package:
            return __import__(module_name, fromlist=[package])
        else:
            return __import__(module_name)
    except ImportError as e:
        print(f"Failed to import {module_name}: {e}")
        return None


def check_required_files(required_files):
    """
    Check if all required files exist in the current directory.

    Args:
        required_files (list): List of required file names

    Returns:
        tuple: (bool, list) - (all_exist, missing_files)
    """
    try:
        current_dir = Path(__file__).parent
    except NameError:
        current_dir = Path.cwd()

    missing_files = []
    
    for filename in required_files:
        file_path = current_dir / filename
        if not file_path.exists():
            missing_files.append(filename)
    
    return len(missing_files) == 0, missing_files


def print_import_debug_info():
    """Print debugging information for import issues."""
    try:
        current_dir = Path(__file__).parent.absolute()
    except NameError:
        current_dir = Path.cwd().absolute()
    
    print("=== Import Debug Information ===")
    print(f"Current file directory: {current_dir}")
    print(f"Current working directory: {Path.cwd()}")
    print(f"Python executable: {sys.executable}")
    print(f"Python version: {sys.version}")
    print("\nPython path:")
    for i, path in enumerate(sys.path):
        print(f"  {i}: {path}")
    
    print("\nFiles in current directory:")
    try:
        for file_path in sorted(current_dir.glob("*.py")):
            print(f"  {file_path.name}")
    except Exception as e:
        print(f"  Error listing files: {e}")
    
    print("=" * 35)


# Required files for the ARINC Flight Simulator
REQUIRED_FILES = [
    "arinc_flight_simulator.py",
    "ui.py", 
    "flight_model.py",
    "A429_utilities.py",
    "airports_data.py",
    "config_manager.py"
]


def validate_environment():
    """
    Validate that the environment is set up correctly for the simulator.
    
    Returns:
        bool: True if environment is valid, False otherwise
    """
    setup_local_imports()
    
    all_exist, missing_files = check_required_files(REQUIRED_FILES)
    
    if not all_exist:
        print("ERROR: Missing required files:")
        for filename in missing_files:
            print(f"  - {filename}")
        print("\nPlease ensure all required files are in the same directory.")
        print_import_debug_info()
        return False
    
    return True
