"""
ARINC Flight Simulator - Core Simulator Module

This module contains the main ArincFlightSimulator class that orchestrates
the flight simulation, UI management, and ARINC 429 data generation.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import os
from typing import Optional, Dict, Any

# Try to import PbaPro - if it fails, we'll run in simulation-only mode
try:
    from ppbase import *
    PBAPRO_AVAILABLE = True
except ImportError:
    PBAPRO_AVAILABLE = False

from ..ui.interface import create_ui
from .flight_model import simulate_flight_data
from ..arinc.utilities import encode_flight_info
from .config import ConfigManager, ConfigDialog


class ArincFlightSimulator:
    """
    Main ARINC Flight Simulator class.
    
    This class manages the flight simulation, UI interactions, and ARINC 429 data output.
    It supports both PbaPro hardware integration and simulation-only mode.
    """
    
    # ARINC labels that correspond to flight data - initialized from config
    AllLabels = []

    def __init__(self, config_file: Optional[str] = None):
        """Initialize the ARINC Flight Simulator."""
        self.running = False
        self.flight_thread: Optional[threading.Thread] = None
        self.time_since_takeoff = 0

        # Initialize configuration manager with provided config file path
        self.config_manager = ConfigManager(config_file) if config_file else ConfigManager()

        # Load configuration values
        self._load_configuration()

        # Initialize AllLabels from configuration
        self.update_labels_from_config()

        # PbaPro related variables
        self.pbapro_initialized = False
        self.labels = []

        # Create the UI first
        self.root = create_ui()

        # Initialize PbaPro if available
        self.init_pbapro()

        # Add simulation controls to the existing UI
        self.add_controls_to_ui()

    def _load_configuration(self) -> None:
        """Load configuration values from config manager."""
        self.update_interval = self.config_manager.get_float('GENERAL', 'update_interval')
        self.time_acceleration = self.config_manager.get_int('GENERAL', 'time_acceleration')
        self.pbapro_board_name = self.config_manager.get_value('GENERAL', 'pbapro_board_name')
        self.random_seed = self.config_manager.get_int('GENERAL', 'random_seed')
        self.movement_margin = self.config_manager.get_float('GENERAL', 'movement_margin')

    def run(self) -> None:
        """Start the GUI main loop."""
        self.root.mainloop()

    def init_pbapro(self) -> None:
        """Initialize PbaPro ARINC system if available."""
        if not PBAPRO_AVAILABLE:
            print("PbaPro not available - running in simulation-only mode")
            return

        try:
            # Initialize PbaPro objects similar to LSY_Arinc_TestSetup
            self.A429Resource = PbaProObject(self.pbapro_board_name)
            self.GlobalConnections = PbaProObject("GlobalConnections")
            self.DBSPrjFile = PbaProObject("DBSPrjFile")

            # Initialize the system
            self.GlobalConnections.Reset()
            self.DBSPrjFile.Init()
            self.A429Resource.Init()

            # Set stream ID
            self.A429Resource.DbsStreamId = "ARINC_1"

            print("PbaPro basic initialization successful")

            # Setup basic ARINC configuration - only mark as initialized if this succeeds
            if self.setup_arinc_labels():
                self.pbapro_initialized = True
                print("PbaPro initialization completed successfully")
            else:
                print("PbaPro basic initialization succeeded but ARINC labels setup failed")

        except Exception as e:
            print(f"Failed to initialize PbaPro: {e}")
            self.pbapro_initialized = False

    def setup_arinc_labels(self) -> bool:
        """Setup ARINC labels for flight parameters.

        Returns:
            bool: True if setup was successful, False otherwise
        """
        if not PBAPRO_AVAILABLE:
            return False

        try:
            # Load database files
            current_dir = os.path.dirname(os.path.abspath(__file__))
            try:
                self.DBSPrjFile.AddFile(os.path.join(current_dir, "Combined.ppdbs"), 0, "")
                self.DBSPrjFile.LoadAll()
                print("Database file (Combined.ppdbs) loaded successfully")
            except Exception as e:
                print(f"Warning: Could not load Combined.ppdbs database file: {e}")
                print("Continuing without database file...")

            DBS = PbaProObject("Parameters")
            self.labels = []

            # Get channel configuration from config manager
            channel_config = self.config_manager.get_channel_config()

            # Get label to channel mapping from config manager
            label_channel_mapping = self.config_manager.get_label_mapping()

            # Setup channels first
            for channel_no, speed in channel_config.items():
                try:
                    channel = self.A429Resource.GetChannel(channel_no)
                    channel.ChannelType = "Tx"
                    channel.ChannelSpeed = speed
                    channel.DbsChannelId = 1
                    print(f"Configured Channel {channel_no} with speed {speed}")
                except Exception as e:
                    print(f"Failed to configure channel {channel_no}: {e}")

            # Create labels for each configured label
            for label_name in self.AllLabels:
                if label_name in label_channel_mapping:
                    channel_no, label_string = label_channel_mapping[label_name]
                    try:
                        channel = self.A429Resource.GetChannel(channel_no)

                        # Determine label number
                        if label_string == "x":
                            # Get label from database
                            try:
                                DBSParam = DBS.GetChild("*." + label_name)
                                label_no = DBSParam.parent().Label  # Label No decimal representation
                            except:
                                # Fallback to sequential numbering if database lookup fails
                                label_no = 100 + self.AllLabels.index(label_name)
                        else:
                            # Parse octal label number
                            label_no = int(label_string, 8)

                        # Create the label
                        label = channel.Send.Setup.LabelList.NewLabel(label_no)
                        label.objectName = label_name

                        status_label = label.GetStatusLabel()
                        self.labels.append(status_label)

                        print(f"Created label for {label_name} on Channel {channel_no}, Label {label_no}")

                    except Exception as e:
                        print(f"Failed to create label for {label_name}: {e}")

            # Sync and enable transmission
            self.A429Resource.DBSSync()
            self.A429Resource.AllTxCtrl(True)

            print(f"Successfully created {len(self.labels)} ARINC labels")
            return True

        except Exception as e:
            print(f"Failed to setup ARINC labels: {e}")
            return False

    def add_controls_to_ui(self) -> None:
        """Add simulation controls to the UI."""
        # Add a frame for simulation controls (positioned directly after mainframe)
        control_frame = ttk.LabelFrame(self.root, text="Simulation Controls")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=10, pady=(0, 10))

        # Start button
        self.start_button = ttk.Button(control_frame, text="Start Simulation", command=self.start_simulation)
        self.start_button.grid(row=0, column=0, padx=5, pady=5)

        # Stop button
        self.stop_button = ttk.Button(control_frame, text="Stop Simulation", command=self.stop_simulation)
        self.stop_button.grid(row=0, column=1, padx=5, pady=5)
        self.stop_button.config(state=tk.DISABLED)

        # Status label
        self.status_var = tk.StringVar(value="Ready")
        ttk.Label(control_frame, textvariable=self.status_var).grid(row=0, column=2, padx=5, pady=5)

        # PbaPro status label
        pbapro_status = "PbaPro: Connected" if self.pbapro_initialized else "PbaPro: Simulation Only"
        self.pbapro_status_var = tk.StringVar(value=pbapro_status)
        pbapro_label = ttk.Label(control_frame, textvariable=self.pbapro_status_var)
        pbapro_label.grid(row=0, column=3, padx=5, pady=5)

        # Color code the PbaPro status
        if self.pbapro_initialized:
            pbapro_label.configure(foreground="green")
        else:
            pbapro_label.configure(foreground="orange")

        # Configuration button (top right)
        self.config_button = ttk.Button(control_frame, text="Configuration", command=self.show_config_dialog)
        self.config_button.grid(row=0, column=4, padx=5, pady=5)

        # Progress bar
        self.progress_var = tk.DoubleVar(value=0.0)
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=1.0)
        self.progress_bar.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), padx=5, pady=5)

        # Output frame (this is the only frame that should expand vertically)
        output_frame = ttk.LabelFrame(self.root, text="ARINC Data Output")
        output_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)

        # Text widget for output
        self.output_text = tk.Text(output_frame, height=15, width=60)
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbar
        scrollbar = ttk.Scrollbar(output_frame, orient=tk.VERTICAL, command=self.output_text.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.output_text.configure(yscrollcommand=scrollbar.set)

        # Configure grid weights - only output frame (row 2) should expand vertically
        self.root.grid_rowconfigure(0, weight=0)  # Mainframe - fixed size
        self.root.grid_rowconfigure(1, weight=0)  # Control frame - fixed size
        self.root.grid_rowconfigure(2, weight=1)  # Output frame - expandable
        self.root.grid_columnconfigure(0, weight=1)  # Allow horizontal expansion

        # Configure output frame internal expansion
        output_frame.grid_rowconfigure(0, weight=1)
        output_frame.grid_columnconfigure(0, weight=1)

    def start_simulation(self) -> None:
        """Start the flight simulation."""
        # Import UI variables
        from ..ui import interface

        # Check if UI variables are available
        if not interface.start_var or not interface.dest_var or not interface.flight_var or not interface.daytime_var:
            messagebox.showerror("Error", "UI not properly initialized. Please restart the application.")
            return

        # Get input values
        start_airport_name = interface.start_var.get()
        dest_airport_name = interface.dest_var.get()
        flight_number = interface.flight_var.get()
        daytime = interface.daytime_var.get()

        # Validate airport selection
        if not start_airport_name or not dest_airport_name:
            messagebox.showerror("Error", "Please select both start and destination airports")
            return

        # Convert airport names to ICAO codes
        if not interface.name_to_icao:
            messagebox.showerror("Error", "Airport data not properly loaded. Please restart the application.")
            return

        start_icao = interface.name_to_icao.get(start_airport_name, "")
        dest_icao = interface.name_to_icao.get(dest_airport_name, "")

        if not start_icao or not dest_icao:
            messagebox.showerror("Error", "Please select valid airports")
            return

        # Validate that destination is different from start
        if start_icao == dest_icao:
            messagebox.showerror("Error", "Destination airport cannot be the same as start airport")
            return

        # Initialize simulation
        self.time_since_takeoff = 0
        self.progress_var.set(0)  # Reset progress bar
        self.start_airport = start_icao
        self.dest_airport = dest_icao
        self.flight_number = flight_number
        self.city_pair = f"{start_icao[:3]}{dest_icao[:3]}"  # Create city pair from ICAO codes

        # Update UI
        self.status_var.set("Initializing...")
        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, f"Initializing simulation for flight {flight_number}\n")
        self.output_text.insert(tk.END, f"Route: {start_airport_name} ({start_icao}) → {dest_airport_name} ({dest_icao})\n")
        self.output_text.insert(tk.END, f"Departure time: {daytime}\n")
        self.output_text.insert(tk.END, f"Simulation time reset to: {self.time_since_takeoff} seconds\n\n")
        self.output_text.insert(tk.END, "Setting up ARINC labels...\n")
        self.output_text.see(tk.END)

        # Simulate ARINC board initialization (similar to LSY_Arinc_TestSetup)
        self.output_text.insert(tk.END, "Initializing ARINC board...\n")
        self.output_text.insert(tk.END, "Setting up channels...\n")
        self.output_text.insert(tk.END, "Configuring labels...\n")

        # Show PbaPro status in output
        if self.pbapro_initialized:
            self.output_text.insert(tk.END, f"PbaPro connected - {len(self.labels)} labels configured\n")
            self.output_text.insert(tk.END, "Data will be sent to PbaPro database\n\n")
        else:
            self.output_text.insert(tk.END, "PbaPro not available - simulation only mode\n")
            self.output_text.insert(tk.END, "Data will be displayed in UI only\n\n")

        self.output_text.see(tk.END)

        # Start simulation thread
        self.running = True
        self.flight_thread = threading.Thread(target=self.run_simulation)
        self.flight_thread.daemon = True
        self.flight_thread.start()

        # Update button states
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.config_button.config(state=tk.DISABLED)  # Disable config during simulation

    def stop_simulation(self) -> None:
        """Stop the flight simulation."""
        self.running = False
        if self.flight_thread:
            self.flight_thread.join(timeout=1.0)

        # Reset simulation state for next run
        self.time_since_takeoff = 0
        self.progress_var.set(0)  # Reset progress bar

        self.status_var.set("Stopped")
        self.output_text.insert(tk.END, "\nSimulation stopped\n")
        self.output_text.insert(tk.END, "Ready for next simulation\n")
        self.output_text.see(tk.END)

        # Update button states
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.config_button.config(state=tk.NORMAL)  # Re-enable config when stopped

    def run_simulation(self) -> None:
        """Main simulation loop."""
        self.status_var.set("Running")

        while self.running:
            # Get flight data
            flight_data = simulate_flight_data(
                self.start_airport,
                self.dest_airport,
                self.time_since_takeoff,
                self.random_seed,
                self.movement_margin
            )

            # Update progress bar
            self.progress_var.set(flight_data["progress"])

            # Generate ARINC words for flight info
            arinc_words = encode_flight_info(self.flight_number, self.city_pair)

            # Generate ARINC words for flight parameters
            # This would simulate what LSY_Arinc_TestSetup does with its labels
            self.update_arinc_data(flight_data, arinc_words)

            # Update actual ARINC labels if PbaPro is available
            self.update_arinc_labels(flight_data)

            # Increment simulation time
            self.time_since_takeoff += int(self.update_interval * self.time_acceleration)

            # Check if flight is complete
            if flight_data["progress"] >= 1.0:
                self.running = False
                self.status_var.set("Completed")
                self.output_text.insert(tk.END, "\nFlight completed\n")
                self.output_text.see(tk.END)
                self.start_button.config(state=tk.NORMAL)
                self.stop_button.config(state=tk.DISABLED)
                self.config_button.config(state=tk.NORMAL)  # Re-enable config when completed
                break

            time.sleep(self.update_interval)

    def update_arinc_data(self, flight_data: Dict[str, Any], arinc_words: Dict[str, int]) -> None:
        """Update the ARINC data display."""
        # Clear output
        self.output_text.delete(1.0, tk.END)

        # Display flight status
        self.output_text.insert(tk.END, f"Flight: {self.flight_number} ({self.city_pair})\n")
        self.output_text.insert(tk.END, f"Phase: {flight_data['Flight_Phase']}\n")
        self.output_text.insert(tk.END, f"Progress: {flight_data['progress']*100:.1f}%\n")
        self.output_text.insert(tk.END, f"ETA: {flight_data['ETA_seconds']//60} minutes\n\n")

        # Display ARINC data that would be sent to the board
        self.output_text.insert(tk.END, "ARINC 429 Data Output:\n")
        self.output_text.insert(tk.END, "-" * 50 + "\n")

        # Flight info words (encoded in ARINC 429 format)
        self.output_text.insert(tk.END, "Encoded Flight Info (ARINC 429 Words):\n")
        for key, value in arinc_words.items():
            self.output_text.insert(tk.END, f"{key}: {value:06X}\n")

        # Flight label values (assigned directly to labels, no encoding)
        self.output_text.insert(tk.END, "-" * 50 + "\n")
        self.output_text.insert(tk.END, "Flight Labels (Direct Label Values):\n")

        # Map flight data to ARINC labels using configuration
        arinc_labels = {}

        # Get current label mapping from configuration
        label_mapping = self.config_manager.get_label_mapping()

        # Calculate temperature value (used for Temperature simulated value)
        temperature_value = 15.0 - (flight_data["Altitude_of_aircraft_ft"] / 1000) * 2

        # Add temperature and encoded flight info to flight data for mapping
        flight_data_with_extras = flight_data.copy()
        flight_data_with_extras["Static_Air_Temperature"] = temperature_value

        # Add encoded flight info values
        for key, value in arinc_words.items():
            # Extract the encoded value type from the key (e.g., "FlightNr0", "CityPair1")
            if "FlightNr" in key:
                flight_nr_index = key.split(":")[0].replace("FlightNr", "")
                flight_data_with_extras[f"FlightNr{flight_nr_index}"] = value
            elif "CityPair" in key:
                city_pair_index = key.split(":")[0].replace("CityPair", "")
                flight_data_with_extras[f"CityPair{city_pair_index}"] = value

        # Map each configured label to its simulated value
        for label_name, (channel, label_id, simulated_value) in label_mapping.items():
            # Get the flight data key for this simulated value
            flight_data_key = self.config_manager.get_flight_data_key(simulated_value)

            # Get the value from flight data
            if flight_data_key in flight_data_with_extras:
                arinc_labels[label_name] = flight_data_with_extras[flight_data_key]
            else:
                # Fallback to 0.0 if key not found
                arinc_labels[label_name] = 0.0

        # Display the labels that would be sent directly to ARINC labels (no encoding)
        for label_name, value in arinc_labels.items():
            # Flight data is assigned directly to label values without encoding
            self.output_text.insert(tk.END, f"{label_name}: {value:.6f}\n")

        self.output_text.see(tk.END)

    def update_arinc_labels(self, flight_data: Dict[str, Any]) -> None:
        """Update actual ARINC labels with flight data if PbaPro is available."""
        if not self.pbapro_initialized or not self.labels:
            return

        try:
            # Get current label mapping from configuration
            label_mapping = self.config_manager.get_label_mapping()

            # Calculate temperature value (used for Temperature simulated value)
            temperature_value = 15.0 - (flight_data["Altitude_of_aircraft_ft"] / 1000) * 2

            # Generate encoded flight info
            arinc_words = encode_flight_info(self.flight_number, self.city_pair)

            # Add temperature and encoded flight info to flight data for mapping
            flight_data_with_extras = flight_data.copy()
            flight_data_with_extras["Static_Air_Temperature"] = temperature_value

            # Add encoded flight info values
            for key, value in arinc_words.items():
                # Extract the encoded value type from the key (e.g., "FlightNr0", "CityPair1")
                if "FlightNr" in key:
                    flight_nr_index = key.split(":")[0].replace("FlightNr", "")
                    flight_data_with_extras[f"FlightNr{flight_nr_index}"] = value
                elif "CityPair" in key:
                    city_pair_index = key.split(":")[0].replace("CityPair", "")
                    flight_data_with_extras[f"CityPair{city_pair_index}"] = value

            # Build label values using configuration mapping
            label_values = {}
            for label_name, (_, _, simulated_value) in label_mapping.items():
                # Get the flight data key for this simulated value
                flight_data_key = self.config_manager.get_flight_data_key(simulated_value)

                # Get the value from flight data
                if flight_data_key in flight_data_with_extras:
                    label_values[label_name] = flight_data_with_extras[flight_data_key]
                else:
                    # Fallback to 0.0 if key not found
                    label_values[label_name] = 0.0

            # Update each label with its corresponding value
            for label in self.labels:
                label_name = label.objectName
                if label_name in label_values:
                    try:
                        # Get the label reference
                        label_ref = label.GetChild("Parameters." + label_name)
                        if label_ref:
                            # Set the value directly (no encoding)
                            label_ref.ValueStr = str(label_values[label_name])
                    except Exception as e:
                        print(f"Failed to update label {label_name}: {e}")

        except Exception as e:
            print(f"Error updating ARINC labels: {e}")

    def show_config_dialog(self) -> None:
        """Show configuration dialog."""
        try:
            dialog = ConfigDialog(self.root, self.config_manager)
            result = dialog.show_dialog()

            if result:  # Configuration was saved
                # Apply configuration changes immediately
                self.apply_configuration_changes()
                messagebox.showinfo("Configuration", "Configuration updated and applied successfully!\n\nNote: PbaPro hardware changes require restarting the simulator.")
        except Exception as e:
            messagebox.showerror("Configuration Error", f"Failed to open configuration dialog: {e}")

    def apply_configuration_changes(self) -> None:
        """Apply configuration changes immediately to the running simulator."""
        print("Applying configuration changes...")

        # Store old values for comparison
        old_update_interval = self.update_interval
        old_time_acceleration = self.time_acceleration
        old_random_seed = self.random_seed
        old_movement_margin = self.movement_margin
        old_board_name = self.pbapro_board_name

        # Reload configuration values from config manager
        self.config_manager.load_config()  # Reload from file
        self._load_configuration()  # Update instance variables

        # Report what changed
        changes = []
        if old_update_interval != self.update_interval:
            changes.append(f"Update interval: {old_update_interval}s → {self.update_interval}s")
        if old_time_acceleration != self.time_acceleration:
            changes.append(f"Time acceleration: {old_time_acceleration}x → {self.time_acceleration}x")
        if old_random_seed != self.random_seed:
            changes.append(f"Random seed: {old_random_seed} → {self.random_seed}")
        if old_movement_margin != self.movement_margin:
            changes.append(f"Movement margin: {old_movement_margin} → {self.movement_margin}")
        if old_board_name != self.pbapro_board_name:
            changes.append(f"PbaPro board: {old_board_name} → {self.pbapro_board_name}")

        # Log changes
        if changes:
            print("Configuration changes applied:")
            for change in changes:
                print(f"  - {change}")
        else:
            print("No configuration changes detected")

        # Check if PbaPro reinitialization is needed
        if old_board_name != self.pbapro_board_name and PBAPRO_AVAILABLE:
            print("PbaPro board name changed - restart simulator for hardware changes to take effect")

        # Update label configuration
        self.update_labels_from_config()

        # Update label configuration if simulation is not running
        if not self.running:
            try:
                # Reinitialize labels with new configuration
                if self.pbapro_initialized:
                    print("Updating ARINC label configuration...")
                    if self.setup_arinc_labels():
                        print("ARINC label configuration updated successfully")
                    else:
                        print("Warning: Failed to update ARINC label configuration")
            except Exception as e:
                print(f"Warning: Failed to update ARINC labels: {e}")
        else:
            print("Simulation is running - label changes will apply to next simulation")

    def update_labels_from_config(self) -> None:
        """Update the AllLabels list from current configuration."""
        # Get current label mapping from configuration
        label_mapping = self.config_manager.get_label_mapping()

        # Update AllLabels to match configured labels
        old_labels = self.AllLabels.copy()
        self.AllLabels = list(label_mapping.keys())

        # Report changes
        added_labels = set(self.AllLabels) - set(old_labels)
        removed_labels = set(old_labels) - set(self.AllLabels)

        if added_labels:
            print(f"Added labels: {', '.join(added_labels)}")
        if removed_labels:
            print(f"Removed labels: {', '.join(removed_labels)}")
        if not added_labels and not removed_labels:
            print("No label changes detected")
