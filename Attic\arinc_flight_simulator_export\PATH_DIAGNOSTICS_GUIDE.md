# Python Path Diagnostics Guide

This guide explains how to diagnose and fix Python import issues using the enhanced diagnostic tools provided with the ARINC Flight Simulator.

## Quick Diagnosis

If you're experiencing import errors like `ModuleNotFoundError: No module named 'ui'`, run these commands in order:

```bash
# 1. Comprehensive path analysis
python diagnose_paths.py

# 2. Import testing with path information
python test_imports.py

# 3. Environment setup and validation
python setup_environment.py
```

## Understanding the Problem

The error occurs when Python cannot find the simulator modules because:

1. **Wrong Working Directory**: You're running the script from a different directory than where the files are located
2. **Missing sys.path Entry**: The simulator directory is not in Python's search path
3. **String Execution Context**: Some IDEs execute scripts in contexts where `__file__` is not defined

## Diagnostic Tools

### 1. `diagnose_paths.py` - Comprehensive Path Analysis

**Purpose**: Provides detailed information about Python's package search paths

**What it shows**:
- Python installation details
- Current working directory vs script directory
- Complete sys.path analysis with file existence checks
- Location of simulator files in the path
- Import mechanism testing
- Specific recommendations

**Example output**:
```
Python executable: C:\Python310\python.exe
Current working directory: C:\Program Files\AIM GmbH\pbapro-V03.00
Script directory: C:\Users\<USER>\Desktop\arinc_flight_simulator_ES25_export

SYS.PATH ANALYSIS (10 entries):
 0: ✗ [MISSING] C:\Program Files\AIM GmbH\pbapro-V03.00
 1: ✓ [DIR    ] C:\Python310\lib
...

SIMULATOR FILE SEARCH:
✗ ui.py: not found in any sys.path location
```

### 2. `test_imports.py` - Import Testing with Path Info

**Purpose**: Tests actual imports and shows path setup process

**What it shows**:
- Python path before and after setup
- Search for simulator files in sys.path
- Individual module import results
- UI creation testing

### 3. `setup_environment.py` - Environment Validation

**Purpose**: Comprehensive environment check and setup

**What it shows**:
- Python version compatibility
- Required package availability
- File structure validation
- Python environment analysis

## Common Scenarios and Solutions

### Scenario 1: Running from Wrong Directory

**Problem**: 
```
Current working directory: C:\Program Files\AIM GmbH\pbapro-V03.00
Script directory: C:\Users\<USER>\Desktop\arinc_flight_simulator_ES25_export
```

**Solution**:
```bash
cd "C:\Users\<USER>\Desktop\arinc_flight_simulator_ES25_export"
python test_imports.py
```

### Scenario 2: IDE String Execution

**Problem**: `NameError: name '__file__' is not defined`

**Solution**: The enhanced scripts now handle this automatically with fallback mechanisms.

### Scenario 3: Files Not in sys.path

**Problem**: Simulator files exist but not found in any sys.path location

**Solutions**:
1. **Use the universal launcher**:
   ```bash
   python find_and_run.py
   ```

2. **Add directory to PYTHONPATH**:
   ```bash
   # Windows
   set PYTHONPATH=%PYTHONPATH%;C:\path\to\simulator
   
   # Linux/Mac
   export PYTHONPATH=$PYTHONPATH:/path/to/simulator
   ```

3. **Use the enhanced import utilities** (automatic in updated scripts)

## Universal Solutions

### Option 1: Universal Launcher (Recommended)

The `find_and_run.py` script can locate and run the simulator from anywhere:

```bash
python find_and_run.py          # Run simulator
python find_and_run.py test     # Run tests
python find_and_run.py setup    # Run setup
```

### Option 2: Copy Diagnostic Script

Copy `diagnose_paths.py` to any location and run it to find the simulator:

```bash
# Copy the script to your current location
copy "C:\path\to\simulator\diagnose_paths.py" .
python diagnose_paths.py
```

### Option 3: Manual Path Setup

Add this code to the beginning of any script:

```python
import sys
from pathlib import Path

# Add simulator directory to path
simulator_dir = Path(r"C:\Users\<USER>\Desktop\arinc_flight_simulator_ES25_export")
if simulator_dir.exists() and str(simulator_dir) not in sys.path:
    sys.path.insert(0, str(simulator_dir))
```

## Interpreting Diagnostic Output

### Good Output (Working):
```
✓ All simulator files found in current directory
✓ ui: found at /path/to/simulator/ui.py
✓ All required modules imported successfully!
```

### Problem Output (Needs Fix):
```
✗ ui.py: not found in any sys.path location
✗ Failed to import ui: No module named 'ui'
Current working directory: C:\Program Files\AIM GmbH\pbapro-V03.00
```

### Key Indicators:
- **✓ Green checkmarks**: Everything working
- **✗ Red X marks**: Problems found
- **⚠ Yellow warnings**: Potential issues
- **ℹ Blue info**: Informational messages

## Advanced Troubleshooting

### Check Python Installation
```bash
python --version
python -c "import sys; print(sys.executable)"
python -c "import sys; print('\n'.join(sys.path))"
```

### Verify File Permissions
```bash
# Windows
dir /Q "C:\path\to\simulator\*.py"

# Linux/Mac
ls -la /path/to/simulator/*.py
```

### Test Import Manually
```python
import sys
sys.path.insert(0, r"C:\path\to\simulator")
import ui  # Should work if path is correct
```

## Getting Help

If you're still having issues after running the diagnostics:

1. **Run all diagnostic tools** and save the output
2. **Check the recommendations** provided by each tool
3. **Verify file locations** using the path analysis
4. **Use the universal launcher** as a workaround
5. **Contact support** with the diagnostic output

## Files Overview

- `diagnose_paths.py` - Comprehensive path analysis
- `test_imports.py` - Import testing with path info
- `setup_environment.py` - Environment validation
- `find_and_run.py` - Universal launcher
- `import_utils.py` - Enhanced import utilities
- `PATH_DIAGNOSTICS_GUIDE.md` - This guide
