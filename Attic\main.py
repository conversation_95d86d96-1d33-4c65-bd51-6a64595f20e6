from flight_model import simulate_flight_data
import os
import time

start_airport = "EDDF"
destination_airport = "KJFK"
time_since_takeoff = 8000
random_seed = 123

while (True):       
    flight_data = simulate_flight_data(start_airport, destination_airport, time_since_takeoff, random_seed)
    os.system('cls' if os.name == 'nt' else 'clear')
    for k, v in flight_data.items():
        print(f"{k}: {v}")
        time_since_takeoff += 1
    time.sleep(0.1)
    if flight_data["ETA_seconds"] <= 0: break