# ARINC 429 Encoding Utilities
# This module provides functions to encode flight information into ARINC 429 words.

def encode_char(char):
    """
    Encode a single character into 7-bit ASCII.
    """
    ascii_val = ord(char)
    if 32 <= ascii_val <= 126:
        return ascii_val & 0x7F  # Ensure 7-bit
    else:
        return ord(' ')  # Replace unsupported characters with space

def encode_char2(char1, char2):
    """
    Encode two characters into an ARINC 429 word with Char 2 format.
    """
    c1 = encode_char(char1)
    c2 = encode_char(char2)
    word = 0

    word |= (c1 & 0x7F) << 2
    word |= (c2 & 0x7F) << 10    

    return word

def encode_char3(char1, char2, char3):
    """
    Encode three characters into an ARINC 429 word with Char 3 format.
    """
    c1 = encode_char(char1)
    c2 = encode_char(char2)
    c3 = encode_char(char3)
    word = 0
    
    word |= (c1 & 0x7F)     
    word |= (c2 & 0x7F) << 7     
    word |= (c3 & 0x7F) << 14     
    
    return word

def encode_flight_info(flight_number, city_pair):
    """
    Encode flight number and city pair into ARINC 429 words.
    Flight number is encoded using Char 2 format.
    City pair is encoded using Char 3 format.
    """
    arinc_words = {}

    # Encode flight number
    flight_number = flight_number.ljust(6)  # Pad to 6 characters
    for i in range(0, 6, 2):
        word = encode_char2(flight_number[i], flight_number[i+1])
        arinc_words[f"FlightNr{str(int(i/2))}: {flight_number[i]}{flight_number[i+1]} "] = word

    # Encode city pair
    city_pair = city_pair.ljust(6)  # Pad to 6 characters
    for i in range(0, 6, 3):
        word = encode_char3(city_pair[i], city_pair[i+1], city_pair[i+2])
        arinc_words[f"CityPair{str(int(i/3))}: {city_pair[i]}{city_pair[i+1]}{city_pair[i+2]}"] = word

    return arinc_words
