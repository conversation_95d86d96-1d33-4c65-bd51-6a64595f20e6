import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import os
import sys

# Import and setup local import utilities
try:
    from import_utils import setup_local_imports, validate_environment, print_import_debug_info

    # Setup imports and validate environment
    setup_local_imports()
    if not validate_environment():
        sys.exit(1)

except ImportError:
    # Fallback to manual setup if import_utils is not available
    print("Warning: import_utils not found, using fallback import setup")
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

# Import local modules with error handling
try:
    import ui
    from flight_model import simulate_flight_data
    from A429_utilities import encode_flight_info
    from config_manager import ConfigManager, ConfigDialog
except ImportError as e:
    print(f"ERROR: Failed to import required local modules: {e}")
    print(f"Current directory: {os.path.dirname(os.path.abspath(__file__))}")
    print(f"Python path: {sys.path}")
    print("\nRequired files:")
    print("- ui.py")
    print("- flight_model.py")
    print("- A429_utilities.py")
    print("- config_manager.py")
    print("- airports_data.py")

    # Try to provide more debugging info
    try:
        print_import_debug_info()
    except:
        pass

    sys.exit(1)

# Try to import PbaPro - if it fails, we'll run in simulation-only mode
try:
    from ppbase import *
    PBAPRO_AVAILABLE = True
except ImportError:
    PBAPRO_AVAILABLE = False

class ArincFlightSimulator:
    # ARINC parameters that correspond to flight data
    AllParams = ["Present-Position---Latitude",
                 "Present-Position---Longitude",
                 "Ground-Speed",
                 "True-Heading",
                 "Baro-Corrected-Altitude1",
                 "Static-Air-Temperature"]

    def __init__(self):
        self.running = False
        self.flight_thread = None
        self.time_since_takeoff = 0

        # Initialize configuration manager
        self.config_manager = ConfigManager()

        # Load configuration values
        self.update_interval = self.config_manager.get_float('GENERAL', 'update_interval')
        self.time_acceleration = self.config_manager.get_int('GENERAL', 'time_acceleration')
        self.pbapro_board_name = self.config_manager.get_value('GENERAL', 'pbapro_board_name')
        self.random_seed = self.config_manager.get_int('GENERAL', 'random_seed')
        self.movement_margin = self.config_manager.get_float('GENERAL', 'movement_margin')

        # PbaPro related variables
        self.pbapro_initialized = False
        self.labels = []

        # Create the UI first
        self.root = ui.create_ui()

        # Initialize PbaPro if available
        self.init_pbapro()

        # Add simulation controls to the existing UI
        self.add_controls_to_ui()

    def init_pbapro(self):
        """Initialize PbaPro ARINC system if available."""
        if not PBAPRO_AVAILABLE:
            print("PbaPro not available - running in simulation-only mode")
            return

        try:
            # Initialize PbaPro objects similar to LSY_Arinc_TestSetup
            self.A429Resource = PbaProObject(self.pbapro_board_name)
            self.GlobalConnections = PbaProObject("GlobalConnections")
            self.DBSPrjFile = PbaProObject("DBSPrjFile")

            # Initialize the system
            self.GlobalConnections.Reset()
            self.DBSPrjFile.Init()
            self.A429Resource.Init()

            # Set stream ID
            self.A429Resource.DbsStreamId = "ARINC_1"

            # Setup basic ARINC configuration
            self.setup_arinc_labels()

            self.pbapro_initialized = True
            print("PbaPro initialized successfully")

        except Exception as e:
            print(f"Failed to initialize PbaPro: {e}")
            self.pbapro_initialized = False

    def setup_arinc_labels(self):
        """Setup ARINC labels for flight parameters."""
        if not self.pbapro_initialized:
            return

        try:
            # Load database files
            current_dir = os.path.dirname(os.path.abspath(__file__))
            try:
                self.DBSPrjFile.AddFile(os.path.join(current_dir, "Combined.ppdbs"), 0, "")
                self.DBSPrjFile.LoadAll()
                print("Database file (Combined.ppdbs) loaded successfully")
            except Exception as e:
                print(f"Warning: Could not load Combined.ppdbs database file: {e}")
                print("Continuing without database file...")

            DBS = PbaProObject("Parameters")
            self.labels = []

            # Get channel configuration from config manager
            channel_config = self.config_manager.get_channel_config()

            # Get parameter to channel mapping from config manager
            param_channel_mapping = self.config_manager.get_label_mapping()

            # Setup channels first
            for channel_no, speed in channel_config.items():
                try:
                    channel = self.A429Resource.GetChannel(channel_no)
                    channel.ChannelType = "Tx"
                    channel.ChannelSpeed = speed
                    channel.DbsChannelId = 1
                    print(f"Configured Channel {channel_no} with speed {speed}")
                except Exception as e:
                    print(f"Failed to configure channel {channel_no}: {e}")

            # Create labels for each parameter
            for param in self.AllParams:
                if param in param_channel_mapping:
                    channel_no, label_string = param_channel_mapping[param]
                    try:
                        channel = self.A429Resource.GetChannel(channel_no)

                        # Determine label number
                        if label_string == "x":
                            # Get label from database parameter
                            try:
                                DBSParam = DBS.GetChild("*." + param)
                                label_no = DBSParam.parent().Label  # Label No decimal representation
                            except:
                                # Fallback to sequential numbering if database lookup fails
                                label_no = 100 + self.AllParams.index(param)
                        else:
                            # Parse octal label number
                            label_no = int(label_string, 8)

                        # Create the label
                        label = channel.Send.Setup.LabelList.NewLabel(label_no)
                        label.objectName = param

                        status_label = label.GetStatusLabel()
                        self.labels.append(status_label)

                        print(f"Created label for {param} on Channel {channel_no}, Label {label_no}")

                    except Exception as e:
                        print(f"Failed to create label for {param}: {e}")

            # Sync and enable transmission
            self.A429Resource.DBSSync()
            self.A429Resource.AllTxCtrl(True)

            print(f"Successfully created {len(self.labels)} ARINC labels")

        except Exception as e:
            print(f"Failed to setup ARINC labels: {e}")
            self.pbapro_initialized = False

    def add_controls_to_ui(self):
        # Add a frame for simulation controls (positioned directly after mainframe)
        control_frame = ttk.LabelFrame(self.root, text="Simulation Controls")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=10, pady=(0, 10))

        # Start button
        self.start_button = ttk.Button(control_frame, text="Start Simulation", command=self.start_simulation)
        self.start_button.grid(row=0, column=0, padx=5, pady=5)

        # Stop button
        self.stop_button = ttk.Button(control_frame, text="Stop Simulation", command=self.stop_simulation)
        self.stop_button.grid(row=0, column=1, padx=5, pady=5)
        self.stop_button.config(state=tk.DISABLED)

        # Status label
        self.status_var = tk.StringVar(value="Ready")
        ttk.Label(control_frame, textvariable=self.status_var).grid(row=0, column=2, padx=5, pady=5)

        # PbaPro status label
        pbapro_status = "PbaPro: Connected" if self.pbapro_initialized else "PbaPro: Simulation Only"
        self.pbapro_status_var = tk.StringVar(value=pbapro_status)
        pbapro_label = ttk.Label(control_frame, textvariable=self.pbapro_status_var)
        pbapro_label.grid(row=0, column=3, padx=5, pady=5)

        # Color code the PbaPro status
        if self.pbapro_initialized:
            pbapro_label.configure(foreground="green")
        else:
            pbapro_label.configure(foreground="orange")

        # Configuration button (top right)
        self.config_button = ttk.Button(control_frame, text="Configuration", command=self.show_config_dialog)
        self.config_button.grid(row=0, column=4, padx=5, pady=5)

        # Progress bar
        self.progress_var = tk.DoubleVar(value=0.0)
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=1.0)
        self.progress_bar.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), padx=5, pady=5)

        # Output frame (this is the only frame that should expand vertically)
        output_frame = ttk.LabelFrame(self.root, text="ARINC Data Output")
        output_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)

        # Text widget for output
        self.output_text = tk.Text(output_frame, height=15, width=60)
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbar
        scrollbar = ttk.Scrollbar(output_frame, orient=tk.VERTICAL, command=self.output_text.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.output_text.configure(yscrollcommand=scrollbar.set)

        # Configure grid weights - only output frame (row 2) should expand vertically
        self.root.grid_rowconfigure(0, weight=0)  # Mainframe - fixed size
        self.root.grid_rowconfigure(1, weight=0)  # Control frame - fixed size
        self.root.grid_rowconfigure(2, weight=1)  # Output frame - expandable
        self.root.grid_columnconfigure(0, weight=1)  # Allow horizontal expansion

        # Configure output frame internal expansion
        output_frame.grid_rowconfigure(0, weight=1)
        output_frame.grid_columnconfigure(0, weight=1)
    
    def start_simulation(self):
        # Get input values
        start_airport_name = ui.start_var.get()
        dest_airport_name = ui.dest_var.get()
        flight_number = ui.flight_var.get()
        daytime = ui.daytime_var.get()

        # Validate airport selection
        if not start_airport_name or not dest_airport_name:
            messagebox.showerror("Error", "Please select both start and destination airports")
            return

        # Convert airport names to ICAO codes
        start_icao = ui.name_to_icao.get(start_airport_name, "")
        dest_icao = ui.name_to_icao.get(dest_airport_name, "")

        if not start_icao or not dest_icao:
            messagebox.showerror("Error", "Please select valid airports")
            return
        
        # Initialize simulation
        self.time_since_takeoff = 0
        self.start_airport = start_icao
        self.dest_airport = dest_icao
        self.flight_number = flight_number
        self.city_pair = f"{start_icao[:3]}{dest_icao[:3]}"  # Create city pair from ICAO codes
        
        # Update UI
        self.status_var.set("Initializing...")
        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, f"Initializing simulation for flight {flight_number}\n")
        self.output_text.insert(tk.END, f"Route: {start_airport_name} ({start_icao}) → {dest_airport_name} ({dest_icao})\n")
        self.output_text.insert(tk.END, f"Departure time: {daytime}\n\n")
        self.output_text.insert(tk.END, "Setting up ARINC labels...\n")
        self.output_text.see(tk.END)
        
        # Simulate ARINC board initialization (similar to LSY_Arinc_TestSetup)
        self.output_text.insert(tk.END, "Initializing ARINC board...\n")
        self.output_text.insert(tk.END, "Setting up channels...\n")
        self.output_text.insert(tk.END, "Configuring labels...\n")

        # Show PbaPro status in output
        if self.pbapro_initialized:
            self.output_text.insert(tk.END, f"PbaPro connected - {len(self.labels)} labels configured\n")
            self.output_text.insert(tk.END, "Data will be sent to PbaPro database\n\n")
        else:
            self.output_text.insert(tk.END, "PbaPro not available - simulation only mode\n")
            self.output_text.insert(tk.END, "Data will be displayed in UI only\n\n")

        self.output_text.see(tk.END)
        
        # Start simulation thread
        self.running = True
        self.flight_thread = threading.Thread(target=self.run_simulation)
        self.flight_thread.daemon = True
        self.flight_thread.start()
        
        # Update button states
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.config_button.config(state=tk.DISABLED)  # Disable config during simulation
    
    def stop_simulation(self):
        self.running = False
        if self.flight_thread:
            self.flight_thread.join(timeout=1.0)
        
        self.status_var.set("Stopped")
        self.output_text.insert(tk.END, "\nSimulation stopped\n")
        self.output_text.see(tk.END)
        
        # Update button states
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.config_button.config(state=tk.NORMAL)  # Re-enable config when stopped
    
    def run_simulation(self):
        self.status_var.set("Running")
        
        while self.running:
            # Get flight data
            flight_data = simulate_flight_data(
                self.start_airport,
                self.dest_airport,
                self.time_since_takeoff,
                self.random_seed,
                self.movement_margin
            )
            
            # Update progress bar
            self.progress_var.set(flight_data["progress"])
            
            # Generate ARINC words for flight info
            arinc_words = encode_flight_info(self.flight_number, self.city_pair)
            
            # Generate ARINC words for flight parameters
            # This would simulate what LSY_Arinc_TestSetup does with its labels
            self.update_arinc_data(flight_data, arinc_words)

            # Update actual ARINC labels if PbaPro is available
            self.update_arinc_labels(flight_data)
            
            # Increment simulation time
            self.time_since_takeoff += int(self.update_interval * self.time_acceleration)
            
            # Check if flight is complete
            if flight_data["progress"] >= 1.0:
                self.running = False
                self.status_var.set("Completed")
                self.output_text.insert(tk.END, "\nFlight completed\n")
                self.output_text.see(tk.END)
                self.start_button.config(state=tk.NORMAL)
                self.stop_button.config(state=tk.DISABLED)
                self.config_button.config(state=tk.NORMAL)  # Re-enable config when completed
                break
            
            time.sleep(self.update_interval)
    
    def update_arinc_data(self, flight_data, arinc_words):
        # Clear output
        self.output_text.delete(1.0, tk.END)
        
        # Display flight status
        self.output_text.insert(tk.END, f"Flight: {self.flight_number} ({self.city_pair})\n")
        self.output_text.insert(tk.END, f"Phase: {flight_data['Flight_Phase']}\n")
        self.output_text.insert(tk.END, f"Progress: {flight_data['progress']*100:.1f}%\n")
        self.output_text.insert(tk.END, f"ETA: {flight_data['ETA_seconds']//60} minutes\n\n")
        
        # Display ARINC data that would be sent to the board
        self.output_text.insert(tk.END, "ARINC 429 Data Output:\n")
        self.output_text.insert(tk.END, "-" * 50 + "\n")

        # Flight info words (encoded in ARINC 429 format)
        self.output_text.insert(tk.END, "Encoded Flight Info (ARINC 429 Words):\n")
        for key, value in arinc_words.items():
            self.output_text.insert(tk.END, f"{key}: {value:06X}\n")
        
        # Flight parameter values (assigned directly to labels, no encoding)
        self.output_text.insert(tk.END, "-" * 50 + "\n")
        self.output_text.insert(tk.END, "Flight Parameters (Direct Label Values):\n")

        # Map flight data to ARINC label parameters (direct assignment, no encoding)
        arinc_params = {
            "Present-Position---Latitude": flight_data["Latitude_of_aircraft"],
            "Present-Position---Longitude": flight_data["Longitude_of_aircraft"],
            "Ground-Speed": flight_data["Ground_Speed_knots"],
            "True-Heading": flight_data["Heading_degrees"],
            "Baro-Corrected-Altitude1": flight_data["Altitude_of_aircraft_ft"],
            "Static-Air-Temperature": 15.0 - (flight_data["Altitude_of_aircraft_ft"] / 1000) * 2  # Simple temp model
        }
        
        # Display the parameters that would be sent directly to ARINC labels (no encoding)
        for param_name, value in arinc_params.items():
            # Flight parameters are assigned directly to label values without encoding
            self.output_text.insert(tk.END, f"{param_name}: {value:.6f}\n")
        
        self.output_text.see(tk.END)

    def update_arinc_labels(self, flight_data):
        """Update actual ARINC labels with flight data if PbaPro is available."""
        if not self.pbapro_initialized or not self.labels:
            return

        try:
            # Map flight data to ARINC parameters (direct assignment, no encoding)
            param_values = {
                "Present-Position---Latitude": flight_data["Latitude_of_aircraft"],
                "Present-Position---Longitude": flight_data["Longitude_of_aircraft"],
                "Ground-Speed": flight_data["Ground_Speed_knots"],
                "True-Heading": flight_data["Heading_degrees"],
                "Baro-Corrected-Altitude1": flight_data["Altitude_of_aircraft_ft"],
                "Static-Air-Temperature": 15.0 - (flight_data["Altitude_of_aircraft_ft"] / 1000) * 2
            }

            # Update each label with its corresponding value
            for label in self.labels:
                param_name = label.objectName
                if param_name in param_values:
                    try:
                        # Get the parameter reference for this label
                        param_ref = label.GetChild("Parameters." + param_name)
                        if param_ref:
                            # Set the value directly (no encoding)
                            param_ref.ValueStr = str(param_values[param_name])
                    except Exception as e:
                        print(f"Failed to update label {param_name}: {e}")

        except Exception as e:
            print(f"Error updating ARINC labels: {e}")

    def show_config_dialog(self):
        """Show configuration dialog."""
        dialog = ConfigDialog(self.root, self.config_manager)
        result = dialog.show_dialog()

        if result:  # Configuration was saved
            # Reload configuration values
            self.reload_configuration()
            messagebox.showinfo("Configuration", "Configuration updated successfully!")

    def reload_configuration(self):
        """Reload configuration values from config manager."""
        # Update instance variables
        self.update_interval = self.config_manager.get_float('GENERAL', 'update_interval')
        self.time_acceleration = self.config_manager.get_int('GENERAL', 'time_acceleration')
        self.random_seed = self.config_manager.get_int('GENERAL', 'random_seed')
        self.movement_margin = self.config_manager.get_float('GENERAL', 'movement_margin')
        old_board_name = self.pbapro_board_name
        self.pbapro_board_name = self.config_manager.get_value('GENERAL', 'pbapro_board_name')

        # If board name changed and PbaPro is available, reinitialize
        if old_board_name != self.pbapro_board_name and PBAPRO_AVAILABLE:
            print(f"Board name changed from {old_board_name} to {self.pbapro_board_name}")
            print("PbaPro reinitialization required - restart simulator for changes to take effect")

# Create and run the application
if __name__ == "__main__":
    simulator = ArincFlightSimulator()
    simulator.root.mainloop()
